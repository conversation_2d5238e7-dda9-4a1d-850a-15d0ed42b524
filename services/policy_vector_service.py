"""
HR Policy Vectorization Service.

This service handles the vectorization of HR policies and semantic search.
"""

import os
import logging
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
import psycopg2
from psycopg2.extras import RealDictCursor
import google.generativeai as genai
from google.generativeai.types import HarmCategory, HarmBlockThreshold

# Set up logger
logger = logging.getLogger(__name__)

class PolicyVectorService:
    """Service for vectorizing and searching HR policies."""
    
    def __init__(self, db_url: str = None):
        """Initialize the service.
        
        Args:
            db_url: Database URL. If None, uses the DATABASE_URL environment variable.
        """
        self.db_url = db_url or os.environ.get("DATABASE_URL", "postgresql://rahula:postgres@localhost:5432/postgres")
        self.schema = os.environ.get("DB_SCHEMA", "itsm")
        self.api_key = os.environ.get("GOOGLE_API_KEY")
        
        # Initialize Google Generative AI
        if self.api_key:
            genai.configure(api_key=self.api_key)
            
            # Get the embedding model
            try:
                self.embedding_model = genai.get_embedding_model("models/embedding-001")
                logger.info("Embedding model initialized successfully")
            except Exception as e:
                logger.error(f"Error initializing embedding model: {str(e)}")
                self.embedding_model = None
        else:
            logger.warning("No Google API key provided. Embedding functionality will be limited.")
            self.embedding_model = None
    
    def get_db_connection(self):
        """Get a database connection.
        
        Returns:
            A database connection.
        """
        try:
            conn = psycopg2.connect(self.db_url)
            return conn
        except Exception as e:
            logger.error(f"Error connecting to database: {str(e)}")
            raise
    
    def get_all_policies(self) -> List[Dict[str, Any]]:
        """Get all HR policies from the database.
        
        Returns:
            List of HR policies.
        """
        try:
            conn = self.get_db_connection()
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute(f"SELECT * FROM {self.schema}.hr_policies")
                policies = cursor.fetchall()
            conn.close()
            return policies
        except Exception as e:
            logger.error(f"Error getting HR policies: {str(e)}")
            return []
    
    def chunk_policy(self, policy: Dict[str, Any], chunk_size: int = 1000) -> List[str]:
        """Split a policy into chunks for vectorization.
        
        Args:
            policy: The policy to chunk.
            chunk_size: Maximum size of each chunk in characters.
            
        Returns:
            List of policy chunks.
        """
        content = policy.get('content', '')
        if not content:
            return []
        
        # Simple chunking by character count
        chunks = []
        current_chunk = ""
        
        # Add policy metadata to each chunk for context
        metadata = f"Policy: {policy.get('title', '')}\nCategory: {policy.get('category', '')}\n\n"
        
        for paragraph in content.split('\n'):
            if len(current_chunk) + len(paragraph) > chunk_size - len(metadata):
                if current_chunk:
                    chunks.append(metadata + current_chunk)
                current_chunk = paragraph
            else:
                if current_chunk:
                    current_chunk += '\n'
                current_chunk += paragraph
        
        if current_chunk:
            chunks.append(metadata + current_chunk)
        
        return chunks
    
    def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings for a list of texts.
        
        Args:
            texts: List of texts to generate embeddings for.
            
        Returns:
            List of embeddings.
        """
        if not self.embedding_model:
            logger.error("Embedding model not initialized")
            return [[] for _ in texts]  # Return empty embeddings
        
        try:
            embeddings = []
            for text in texts:
                embedding = self.embedding_model.get_embeddings([text])[0].values
                embeddings.append(embedding)
            return embeddings
        except Exception as e:
            logger.error(f"Error generating embeddings: {str(e)}")
            return [[] for _ in texts]  # Return empty embeddings
    
    def store_policy_vectors(self, policy_id: str, chunks: List[str], embeddings: List[List[float]]) -> bool:
        """Store policy chunks and their embeddings in the database.
        
        Args:
            policy_id: ID of the policy.
            chunks: List of policy chunks.
            embeddings: List of embeddings for each chunk.
            
        Returns:
            True if successful, False otherwise.
        """
        if len(chunks) != len(embeddings):
            logger.error(f"Number of chunks ({len(chunks)}) does not match number of embeddings ({len(embeddings)})")
            return False
        
        try:
            conn = self.get_db_connection()
            with conn.cursor() as cursor:
                # First, delete any existing vectors for this policy
                cursor.execute(f"DELETE FROM {self.schema}.hr_policy_vectors WHERE policy_id = %s", (policy_id,))
                
                # Insert new vectors
                for i, (chunk, embedding) in enumerate(zip(chunks, embeddings)):
                    cursor.execute(
                        f"INSERT INTO {self.schema}.hr_policy_vectors (policy_id, chunk_index, chunk_text, embedding) "
                        f"VALUES (%s, %s, %s, %s)",
                        (policy_id, i, chunk, embedding)
                    )
            
            conn.commit()
            conn.close()
            return True
        except Exception as e:
            logger.error(f"Error storing policy vectors: {str(e)}")
            return False
    
    def vectorize_all_policies(self) -> int:
        """Vectorize all HR policies in the database.
        
        Returns:
            Number of policies vectorized.
        """
        policies = self.get_all_policies()
        count = 0
        
        for policy in policies:
            policy_id = policy.get('policy_id')
            chunks = self.chunk_policy(policy)
            
            if chunks:
                embeddings = self.generate_embeddings(chunks)
                if self.store_policy_vectors(policy_id, chunks, embeddings):
                    count += 1
                    logger.info(f"Vectorized policy {policy_id} with {len(chunks)} chunks")
        
        return count
    
    def search_policies(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """Search for HR policies using semantic search.
        
        Args:
            query: The search query.
            top_k: Number of results to return.
            
        Returns:
            List of matching policy chunks with their policies.
        """
        if not self.embedding_model:
            logger.error("Embedding model not initialized")
            return []
        
        try:
            # Generate embedding for the query
            query_embedding = self.embedding_model.get_embeddings([query])[0].values
            
            # Get all policy vectors
            conn = self.get_db_connection()
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute(
                    f"SELECT v.*, p.title, p.category, p.subcategory "
                    f"FROM {self.schema}.hr_policy_vectors v "
                    f"JOIN {self.schema}.hr_policies p ON v.policy_id = p.policy_id"
                )
                vectors = cursor.fetchall()
            conn.close()
            
            # Calculate cosine similarity
            results = []
            for vector in vectors:
                embedding = vector.get('embedding', [])
                if embedding:
                    similarity = self.cosine_similarity(query_embedding, embedding)
                    results.append({
                        'policy_id': vector.get('policy_id'),
                        'title': vector.get('title'),
                        'category': vector.get('category'),
                        'subcategory': vector.get('subcategory'),
                        'chunk_text': vector.get('chunk_text'),
                        'similarity': similarity
                    })
            
            # Sort by similarity and return top_k
            results.sort(key=lambda x: x.get('similarity', 0), reverse=True)
            return results[:top_k]
        except Exception as e:
            logger.error(f"Error searching policies: {str(e)}")
            return []
    
    @staticmethod
    def cosine_similarity(a: List[float], b: List[float]) -> float:
        """Calculate cosine similarity between two vectors.
        
        Args:
            a: First vector.
            b: Second vector.
            
        Returns:
            Cosine similarity.
        """
        if not a or not b:
            return 0
        
        a = np.array(a)
        b = np.array(b)
        
        return np.dot(a, b) / (np.linalg.norm(a) * np.linalg.norm(b))
