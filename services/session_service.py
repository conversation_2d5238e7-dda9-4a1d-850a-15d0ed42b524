"""
Session Service implementation.

This module contains the InMemorySessionService implementation for the ITSM solution.
"""

import logging
import time
import uuid
from typing import Dict, Any, Optional, Tuple

from google.adk.sessions import InMemorySessionService, Session

# Set up logger
logger = logging.getLogger(__name__)


class EnhancedInMemorySessionService(InMemorySessionService):
    """Enhanced version of InMemorySessionService with additional logging and functionality."""

    def __init__(self):
        """Initialize the EnhancedInMemorySessionService."""
        super().__init__()
        self.session_events = {}  # Store events by session_id
        logger.info("Enhanced session service initialized")

    async def create_session(
        self,
        *,
        app_name: str,
        user_id: str,
        state: Optional[Dict[str, Any]] = None,
        session_id: Optional[str] = None
    ) -> Session:
        """Create a new session with additional logging.

        Args:
            app_name: The name of the application creating the session.
            user_id: The ID of the user for whom the session is created.
            session_id: The ID to assign to the new session. If None, a UUID will be generated.
            state: Optional initial state for the session.

        Returns:
            The newly created session.
        """
        # Generate session_id if not provided
        if session_id is None:
            session_id = str(uuid.uuid4())

        logger.info(f"Creating session: app={app_name}, user={user_id}, session={session_id}")

        # Initialize session events tracking
        self.session_events.setdefault(session_id, []).append({
            "event_type": "session_created",
            "timestamp": time.time(),
            "app_name": app_name,
            "user_id": user_id
        })

        # Create the session with initial state
        initial_state = state or {}

        # Add session metadata to state
        initial_state.update({
            "session_metadata": {
                "created_at": time.time(),
                "app_name": app_name,
                "user_id": user_id,
                "session_id": session_id
            }
        })

        # Create a Session object with the correct field names
        session = Session(
            id=session_id,  # Use id instead of session_id
            app_name=app_name,
            user_id=user_id,
            state=initial_state
        )

        # Store the session in the parent class's session dictionary
        # Use a key tuple that matches what the parent class expects
        key = self._create_session_key(app_name, user_id, session_id)
        self.sessions[key] = session

        logger.info(f"Session created successfully: {session_id}")
        return session

    async def get_session(
        self,
        *,
        app_name: str,
        user_id: str,
        session_id: str,
        config: Optional[Any] = None
    ) -> Optional[Session]:
        """Get a session by ID with additional logging.

        Args:
            app_name: The name of the application.
            user_id: The ID of the user.
            session_id: The ID of the session to retrieve.

        Returns:
            The session if found, None otherwise.
        """
        logger.debug(f"Getting session: app={app_name}, user={user_id}, session={session_id}")

        # Access the session directly from the sessions dictionary
        key = self._create_session_key(app_name, user_id, session_id)
        session = self.sessions.get(key)

        if session:
            # Update last accessed time in metadata
            if "session_metadata" in session.state:
                session.state["session_metadata"]["last_accessed"] = time.time()

            # Log session access
            self.session_events.setdefault(session_id, []).append({
                "event_type": "session_accessed",
                "timestamp": time.time(),
                "app_name": app_name,
                "user_id": user_id
            })

            logger.debug(f"Session {session_id} retrieved successfully")
        else:
            logger.warning(f"Session not found: app={app_name}, user={user_id}, session={session_id}")

        return session

    def update_session_state(
        self,
        app_name: str,
        user_id: str,
        session_id: str,
        state_delta: Dict[str, Any]
    ) -> Optional[Session]:
        """Update a session's state with additional logging.

        Args:
            app_name: The name of the application.
            user_id: The ID of the user.
            session_id: The ID of the session to update.
            state_delta: The changes to apply to the session state.

        Returns:
            The updated session if found, None otherwise.
        """
        logger.debug(f"Updating session state: app={app_name}, user={user_id}, session={session_id}")

        # Log state update
        self.session_events.setdefault(session_id, []).append({
            "event_type": "state_updated",
            "timestamp": time.time(),
            "app_name": app_name,
            "user_id": user_id,
            "state_keys_updated": list(state_delta.keys())
        })

        # Get the session directly
        key = self._create_session_key(app_name, user_id, session_id)
        session = self.sessions.get(key)

        if session:
            # Update the session state
            session.state.update(state_delta)

            # Update last modified time in metadata
            if "session_metadata" in session.state:
                session.state["session_metadata"]["last_modified"] = time.time()

            logger.debug(f"Session {session_id} state updated successfully")
            return session
        else:
            logger.warning(f"Failed to update state for session: {session_id}")
            return None

    def get_session_events(self, session_id: str) -> list:
        """Get events for a specific session.

        Args:
            session_id: The ID of the session.

        Returns:
            List of events for the session.
        """
        return self.session_events.get(session_id, [])

    def _create_session_key(self, app_name: str, user_id: str, session_id: str) -> Tuple[str, str, str]:
        """Create a key for the sessions dictionary.

        Args:
            app_name: The name of the application.
            user_id: The ID of the user.
            session_id: The ID of the session.

        Returns:
            A key tuple for the sessions dictionary.
        """
        return (app_name, user_id, session_id)