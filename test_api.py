"""
Test script for the HR API.

This script tests the HR API endpoints.
"""

import requests
import json
from datetime import datetime, timedelta

# API base URL
BASE_URL = "http://localhost:8002"

def test_leave_request():
    """Test the leave request endpoint."""
    print("\n=== Testing Leave Request ===")

    # Create a leave request
    today = datetime.now().date()
    start_date = (today + timedelta(days=7)).isoformat()
    end_date = (today + timedelta(days=9)).isoformat()

    payload = {
        "employee_email": "<EMAIL>",
        "start_date": start_date,
        "end_date": end_date,
        "leave_type": "annual",
        "reason": "Vacation",
        "half_day": False
    }

    response = requests.post(f"{BASE_URL}/leave/request", json=payload)
    print(f"Status Code: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")

    return response.json()

def test_leave_balance():
    """Test the leave balance endpoint."""
    print("\n=== Testing Leave Balance ===")

    payload = {
        "employee_email": "<EMAIL>"
    }

    response = requests.post(f"{BASE_URL}/leave/balance", json=payload)
    print(f"Status Code: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")

    return response.json()

def test_leave_history():
    """Test the leave history endpoint."""
    print("\n=== Testing Leave History ===")

    payload = {
        "employee_email": "<EMAIL>"
    }

    response = requests.post(f"{BASE_URL}/leave/history", json=payload)
    print(f"Status Code: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")

    return response.json()

def test_policy_search():
    """Test the policy search endpoint."""
    print("\n=== Testing Policy Search ===")

    payload = {
        "query": "leave policy",
        "top_k": 3
    }

    response = requests.post(f"{BASE_URL}/policy/search", json=payload)
    print(f"Status Code: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")

    return response.json()

def test_leave_suggestion():
    """Test the leave suggestion endpoint."""
    print("\n=== Testing Leave Suggestion ===")

    payload = {
        "employee_email": "<EMAIL>",
        "duration_days": 5
    }

    response = requests.post(f"{BASE_URL}/leave/suggest", json=payload)
    print(f"Status Code: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")

    return response.json()

def main():
    """Run all tests."""
    try:
        # Test leave request
        leave_request = test_leave_request()

        # Test leave balance
        leave_balance = test_leave_balance()

        # Test leave history
        leave_history = test_leave_history()

        # Test policy search
        policy_search = test_policy_search()

        # Test leave suggestion
        leave_suggestion = test_leave_suggestion()

        print("\n=== All tests completed successfully ===")
    except Exception as e:
        print(f"\nError: {str(e)}")

if __name__ == "__main__":
    main()
