"""
Runner service implementation.

This module provides a custom runner service for the ITSM solution.
"""

import asyncio
import logging
import os
import time
import uuid
from typing import Dict, Any, Optional, AsyncGenerator, List

from google.adk.agents import Agent
from google.adk.runners import Runner
from google.adk.sessions import Session, BaseSessionService, InMemorySessionService
from google.genai import types

from services.session_service import EnhancedInMemorySessionService

# Set up logger
logger = logging.getLogger(__name__)


class ItsmsRunnerService:
    """ITSM Runner Service for managing agent execution."""

    def __init__(
        self,
        root_agent: Agent,
        app_name: str = "hr_solution",
        session_service: Optional[object] = None
    ):
        """Initialize the ITSM Runner Service.

        Args:
            root_agent: The root agent to use for handling requests
            app_name: The name of the application
            session_service: The session service to use, or None to create a new one
        """
        self.root_agent = root_agent
        self.app_name = app_name

        # Use provided session service or create a default one
        if session_service is None:
            self.session_service = EnhancedInMemorySessionService()
        else:
            self.session_service = session_service

        # Create the runner with the session service
        self.runner = Runner(
            agent=root_agent,
            app_name=app_name,
            session_service=self.session_service
        )

        self.active_sessions = {}

        # Log initialization with session service type
        service_type = type(self.session_service).__name__
        logger.info(f"HR AI Assistant Runner Service initialized with app_name={app_name}, session_service={service_type}")

    async def create_session(
        self,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        initial_state: Optional[Dict[str, Any]] = None
    ) -> Dict[str, str]:
        """Create a new session.

        Args:
            user_id: The user ID, or None to generate a random one
            session_id: The session ID, or None to generate a random one
            initial_state: Optional initial state for the session

        Returns:
            A dictionary with the user_id and session_id
        """
        # Generate IDs if not provided
        if not user_id:
            user_id = f"user_{uuid.uuid4()}"

        if not session_id:
            session_id = f"session_{uuid.uuid4()}"

        # Create session
        session = await self.session_service.create_session(
            app_name=self.app_name,
            user_id=user_id,
            session_id=session_id,
            state=initial_state or {}
        )

        print("session created: ", session.id)

        # Track active session
        self.active_sessions[session_id] = {
            "user_id": user_id,
            "created_at": time.time(),
            "last_activity": time.time()
        }

        print("active sessions found")

        logger.info(f"Created new session: user_id={user_id}, session_id={session_id}")
        return {
            "user_id": user_id,
            "session_id": session_id
        }

    async def get_session(self, user_id: str, session_id: str) -> Optional[Session]:
        """Get an existing session.

        Args:
            user_id: The user ID
            session_id: The session ID

        Returns:
            The session if found, None otherwise
        """
        session = await self.session_service.get_session(
            app_name=self.app_name,
            user_id=user_id,
            session_id=session_id
        )
        # If session not found, create a new one
        if not session:
            logger.warning(f"Session not found: user_id={user_id}, session_id={session_id}. Creating new session.")
            session = await self.session_service.create_session(
                app_name=self.app_name,
                user_id=user_id,
                session_id=session_id,
                state={}
            )
        return session

    async def process_message(
        self,
        message: str,
        user_id: str,
        session_id: str,
        api_auth_token: Optional[Dict[str, Any]] = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Process a user message.

        Args:
            message: The user message
            user_id: The user ID
            session_id: The session ID
            api_auth_token: API authentication token information

        Yields:
            Dictionary with response details for each event
        """
        logger.info(f"Processing message for session {session_id}: {message[:50]}...")

        # Check if session exists and create it if it doesn't
        session = await self.get_session(user_id, session_id)

        print("session found: ", session.id)

        # Store API auth token in session state if provided
        if api_auth_token and session and hasattr(session, 'state'):
            print("store api auth token in session state")
            if not hasattr(session.state, '__setitem__'):
                # If state is not a dict, make it one
                session.state = {}
            session.state["api_auth_token"] = api_auth_token

        # Update active session tracking
        if session_id in self.active_sessions:
            print("update active session tracking")
            self.active_sessions[session_id]["last_activity"] = time.time()
        else:
            # Add to active sessions if not already tracked
            print("add to active sessions if not already tracked")
            self.active_sessions[session_id] = {
                "user_id": user_id,
                "created_at": time.time(),
                "last_activity": time.time()
            }

        # Use LLM intelligence to detect leave-related queries and route to appropriate agent
        try:
            leave_intent = await self._analyze_leave_intent(message)

            if leave_intent['is_leave_related']:
                logger.info("LLM detected leave-related query - calling leave functions directly to avoid 503 errors")

                # Import leave tools
                from tools.leave_tools import get_leave_balance, get_leave_history, get_pending_leaves, request_leave

                # Create tool context from session
                tool_context = {
                    'user_id': user_id,
                    'session_id': session_id,
                    'api_auth_token': api_auth_token
                }

                # Use enhanced LLM analysis to determine specific function to call
                try:
                    specific_intent = await self._analyze_specific_leave_intent(message)
                    logger.info(f"Specific leave intent detected: {specific_intent['function']}")

                    response_content = None

                    if specific_intent['function'] == 'get_pending_leaves':
                        logger.info("Calling get_pending_leaves directly")
                        result = get_pending_leaves(tool_context)
                        if result.get('status') == 'success':
                            pending_data = result.get('data', [])
                            if pending_data:
                                response_content = f"⏳ You have {len(pending_data)} pending leave requests:\n"
                                for leave in pending_data:
                                    response_content += f"• {leave.get('leave_type', 'Unknown')} from {leave.get('start_date', 'N/A')} to {leave.get('end_date', 'N/A')}\n"
                            else:
                                response_content = "⏳ You have no pending leave requests at this time."
                        else:
                            response_content = f"❌ Unable to retrieve pending leaves: {result.get('message', 'Unknown error')}"

                    elif specific_intent['function'] == 'get_leave_balance':
                        logger.info("Calling get_leave_balance directly")
                        result = get_leave_balance(tool_context)
                        if result.get('status') == 'success':
                            balance_data = result.get('data', {})
                            if balance_data:
                                response_content = "📊 Your current leave balance:\n"
                                for leave_type, days in balance_data.items():
                                    response_content += f"• {leave_type.title()}: {days} days\n"
                            else:
                                response_content = "📊 Your current leave balance: No leave data available."
                        else:
                            response_content = f"❌ Unable to retrieve leave balance: {result.get('message', 'Unknown error')}"

                    elif specific_intent['function'] == 'get_leave_history':
                        logger.info("Calling get_leave_history directly")
                        result = get_leave_history(tool_context)
                        if result.get('status') == 'success':
                            history_data = result.get('data', [])
                            if history_data:
                                response_content = "📋 Your recent leave history:\n"
                                for leave in history_data[:5]:  # Show last 5 records
                                    status = leave.get('status', 'Unknown')
                                    response_content += f"• {leave.get('leave_type', 'Unknown')} from {leave.get('start_date', 'N/A')} to {leave.get('end_date', 'N/A')} - {status}\n"
                            else:
                                response_content = "📋 No leave history found."
                        else:
                            response_content = f"❌ Unable to retrieve leave history: {result.get('message', 'Unknown error')}"

                    elif specific_intent['function'] == 'request_leave':
                        logger.info("Leave request detected - need more details from user")
                        response_content = "📝 I can help you request leave! Please provide:\n• Start date (YYYY-MM-DD)\n• End date (YYYY-MM-DD)\n• Leave type (casual, sick, annual, vacation, etc.)\n• Reason for leave\n\nExample: 'Apply sick leave from 2024-06-15 to 2024-06-17 for medical appointment'"

                    else:
                        # Default to leave balance for general leave queries
                        logger.info("General leave query, showing leave balance directly")
                        result = get_leave_balance(tool_context)
                        if result.get('status') == 'success':
                            balance_data = result.get('data', {})
                            if balance_data:
                                response_content = "📊 Your current leave balance:\n"
                                for leave_type, days in balance_data.items():
                                    response_content += f"• {leave_type.title()}: {days} days\n"
                                response_content += "\n💡 You can ask me about:\n• Leave balance\n• Pending leave requests\n• Leave history\n• Apply for leave"
                            else:
                                response_content = "📊 Your current leave balance: No leave data available."
                        else:
                            response_content = f"❌ Unable to retrieve leave information: {result.get('message', 'Unknown error')}"

                    # Return direct response without going through AI
                    if response_content:
                        logger.info("Returning direct leave function response to avoid AI 503 errors")
                        yield {
                            "type": "DirectResponse",
                            "author": "leave_management_system",
                            "is_final": True,
                            "timestamp": time.time(),
                            "content": response_content
                        }
                        return

                except Exception as leave_error:
                    logger.error("Error in direct leave function calls: %s", str(leave_error))
                    # Fall through to normal processing instead of returning error

        except Exception as e:
            logger.error(f"Error in LLM-guided leave routing: {str(e)}")
            # Fall through to normal AI processing if LLM intent detection fails

        # Prepare the user message
        content = types.Content(role='user', parts=[types.Part(text=message)])

        # Process through runner
        try:
            logger.info(f"Running message through runner {user_id}, {session_id}, {content}")
            async for event in self.runner.run_async(
                user_id=user_id,
                session_id=session_id,
                new_message=content
            ):
                # Extract text content if available
                text_content = None
                try:
                    if (hasattr(event, 'content') and event.content and
                        hasattr(event.content, 'parts') and event.content.parts and
                        len(event.content.parts) > 0 and
                        hasattr(event.content.parts[0], 'text') and event.content.parts[0].text):
                        text_content = event.content.parts[0].text
                except (AttributeError, IndexError, TypeError):
                    text_content = None


                # Safely get event type name
                try:
                    event_type = type(event).__name__
                except AttributeError:
                    event_type = "UnknownEvent"

                response = {
                    "type": event_type,
                    "author": getattr(event, 'author', 'unknown'),
                    "is_final": event.is_final_response() if hasattr(event, 'is_final_response') else False,
                    "timestamp": time.time()
                }


                if text_content:
                    response["content"] = text_content
                logger.info("finalizing event")
                if hasattr(event, 'is_final_response') and event.is_final_response():
                    print("final response found")
                    # Add user session data to final response
                    session = await self.get_session(user_id, session_id)
                    if session and hasattr(session, 'state') and session.state:
                        # Get relevant state info (excluding potentially large objects)
                        safe_state = {}
                        for key, value in session.state.items():
                            if key in ["last_ticket_id", "session_metadata"]:
                                safe_state[key] = value

                        if safe_state:
                            response["session_data"] = safe_state
                yield response
        except Exception as e:
            import traceback
            logger.error(f"Error processing message: {str(e)}")
            logger.error(f"Full traceback: {traceback.format_exc()}")

            # Check for specific error types and handle accordingly
            error_str = str(e)

            # If there's an error with the API key, use mock data
            if "API key not valid" in error_str:
                logger.warning("API key not valid, using mock data instead")
                os.environ["USE_MOCK_DATA"] = "true"

                # Create a mock event with a more helpful response
                text_content = "I'm operating in offline mode due to API key issues, but I can still help with basic HR inquiries. I can provide information about leave balances, leave applications, attendance records, and HR policies. How can I assist you today?"

                response = {
                    "type": "MockEvent",
                    "author": "mock_agent",
                    "is_final": True,
                    "timestamp": time.time(),
                    "content": text_content
                }

                logger.info("Yielding mock response due to API key error")
                yield response
            else:
                # For other errors, return an error response
                yield {
                    "type": "error",
                    "message": f"Error processing message: {str(e)}",
                    "timestamp": time.time()
                }

    def get_active_sessions(self) -> Dict[str, Dict[str, Any]]:
        """Get all active sessions.

        Returns:
            Dictionary of active sessions
        """
        return self.active_sessions.copy()

    def get_session_events(self, session_id: str) -> List[Dict[str, Any]]:
        """Get events for a specific session.

        Args:
            session_id: The session ID

        Returns:
            List of events for the session
        """
        return self.session_service.get_session_events(session_id)

    async def _analyze_leave_intent(self, message: str) -> Dict[str, Any]:
        """Use LLM to analyze if a message is leave-related.

        Args:
            message: The user message to analyze

        Returns:
            Dictionary with 'is_leave_related' boolean and 'intent' string
        """
        try:
            # Use a lightweight LLM call for intent detection
            import google.generativeai as genai
            import os

            # Configure the model for intent detection
            genai.configure(api_key=os.getenv('GOOGLE_API_KEY'))
            model = genai.GenerativeModel('gemini-1.5-flash')  # Use faster model for intent detection

            # Create a simple prompt for leave detection
            intent_prompt = f"""
Is this message related to leave management (vacation, sick leave, time off, PTO, leave balance, leave history, leave requests)?

User message: "{message}"

Respond with ONLY: true or false
"""

            # Get response from LLM
            response = model.generate_content(intent_prompt)
            response_text = response.text.strip().lower()

            # Parse simple true/false response
            is_leave_related = response_text == 'true'

            return {
                'is_leave_related': is_leave_related,
                'intent': 'leave_management' if is_leave_related else 'other'
            }

    async def _analyze_specific_leave_intent(self, message: str) -> Dict[str, Any]:
        """Use LLM to determine the specific leave function to call.

        Args:
            message: The user message to analyze

        Returns:
            Dictionary with 'function' string indicating which leave function to call
        """
        try:
            # Use a lightweight LLM call for specific function detection
            import google.generativeai as genai
            import os

            # Configure the model for function detection
            genai.configure(api_key=os.getenv('GOOGLE_API_KEY'))
            model = genai.GenerativeModel('gemini-1.5-flash')  # Use faster model

            # Create a focused prompt for function detection
            function_prompt = f"""
Analyze this leave management message and determine which specific function should be called.

User message: "{message}"

Respond with ONLY one of these function names:
- get_pending_leaves (for: pending leaves, waiting approval, pending applications, pending requests)
- get_leave_balance (for: leave balance, remaining days, available leave, how many days)
- get_leave_history (for: leave history, past leaves, previous leaves, taken leaves)
- request_leave (for: apply leave, request leave, need leave, want leave, submit leave)

Examples:
"show my pending leaves" -> get_pending_leaves
"check my leave balance" -> get_leave_balance
"my leave history" -> get_leave_history
"apply for sick leave" -> request_leave
"how many days do I have" -> get_leave_balance
"pending applications" -> get_pending_leaves

Respond with ONLY the function name, nothing else.
"""

            # Get response from LLM
            response = model.generate_content(function_prompt)
            function_name = response.text.strip()

            # Validate function name
            valid_functions = ['get_pending_leaves', 'get_leave_balance', 'get_leave_history', 'request_leave']
            if function_name in valid_functions:
                return {'function': function_name}
            else:
                return {'function': 'get_leave_balance'}  # Default fallback

        except Exception as e:
            logger.error(f"Error in specific leave intent analysis: {str(e)}")
            # Fallback to keyword-based detection
            message_lower = message.lower()

            if any(word in message_lower for word in ['pending', 'waiting', 'approval', 'applications']):
                return {'function': 'get_pending_leaves'}
            elif any(word in message_lower for word in ['balance', 'remaining', 'available', 'how many', 'days left']):
                return {'function': 'get_leave_balance'}
            elif any(word in message_lower for word in ['history', 'past', 'previous', 'taken']):
                return {'function': 'get_leave_history'}
            elif any(word in message_lower for word in ['apply', 'request', 'need', 'want', 'submit']):
                return {'function': 'request_leave'}
            else:
                return {'function': 'get_leave_balance'}  # Default fallback

        except Exception as e:
            logger.error(f"Error in LLM intent analysis: {str(e)}")
            # Fallback to simple keyword detection if LLM fails
            message_lower = message.lower()
            leave_keywords = ['leave', 'balance', 'vacation', 'sick', 'holiday', 'time off', 'pto', 'absent', 'pending', 'apply']

            is_leave_related = any(keyword in message_lower for keyword in leave_keywords)

            return {
                'is_leave_related': is_leave_related,
                'intent': 'leave_management' if is_leave_related else 'other'
            }

