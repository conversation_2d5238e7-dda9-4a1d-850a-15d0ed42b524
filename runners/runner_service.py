"""
Runner service implementation.

This module provides a custom runner service for the ITSM solution.
"""

import asyncio
import logging
import os
import time
import uuid
from typing import Dict, Any, Optional, AsyncGenerator, List

from google.adk.agents import Agent
from google.adk.runners import Runner
from google.adk.sessions import Session, BaseSessionService, InMemorySessionService
from google.genai import types

from services.session_service import EnhancedInMemorySessionService

# Set up logger
logger = logging.getLogger(__name__)


class ItsmsRunnerService:
    """ITSM Runner Service for managing agent execution."""

    def __init__(
        self,
        root_agent: Agent,
        app_name: str = "hr_solution",
        session_service: Optional[object] = None
    ):
        """Initialize the ITSM Runner Service.

        Args:
            root_agent: The root agent to use for handling requests
            app_name: The name of the application
            session_service: The session service to use, or None to create a new one
        """
        self.root_agent = root_agent
        self.app_name = app_name

        # Use provided session service or create a default one
        if session_service is None:
            self.session_service = EnhancedInMemorySessionService()
        else:
            self.session_service = session_service

        # Create the runner with the session service
        self.runner = Runner(
            agent=root_agent,
            app_name=app_name,
            session_service=self.session_service
        )

        self.active_sessions = {}

        # Log initialization with session service type
        service_type = type(self.session_service).__name__
        logger.info(f"HR AI Assistant Runner Service initialized with app_name={app_name}, session_service={service_type}")

    async def create_session(
        self,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        initial_state: Optional[Dict[str, Any]] = None
    ) -> Dict[str, str]:
        """Create a new session.

        Args:
            user_id: The user ID, or None to generate a random one
            session_id: The session ID, or None to generate a random one
            initial_state: Optional initial state for the session

        Returns:
            A dictionary with the user_id and session_id
        """
        # Generate IDs if not provided
        if not user_id:
            user_id = f"user_{uuid.uuid4()}"

        if not session_id:
            session_id = f"session_{uuid.uuid4()}"

        # Create session
        session = await self.session_service.create_session(
            app_name=self.app_name,
            user_id=user_id,
            session_id=session_id,
            state=initial_state or {}
        )

        print("session created: ", session.id)

        # Track active session
        self.active_sessions[session_id] = {
            "user_id": user_id,
            "created_at": time.time(),
            "last_activity": time.time()
        }

        print("active sessions found")

        logger.info(f"Created new session: user_id={user_id}, session_id={session_id}")
        return {
            "user_id": user_id,
            "session_id": session_id
        }

    async def get_session(self, user_id: str, session_id: str) -> Optional[Session]:
        """Get an existing session.

        Args:
            user_id: The user ID
            session_id: The session ID

        Returns:
            The session if found, None otherwise
        """
        session = await self.session_service.get_session(
            app_name=self.app_name,
            user_id=user_id,
            session_id=session_id
        )
        # If session not found, create a new one
        if not session:
            logger.warning(f"Session not found: user_id={user_id}, session_id={session_id}. Creating new session.")
            session = await self.session_service.create_session(
                app_name=self.app_name,
                user_id=user_id,
                session_id=session_id,
                state={}
            )
        return session

    async def process_message(
        self,
        message: str,
        user_id: str,
        session_id: str,
        api_auth_token: Optional[Dict[str, Any]] = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Process a user message.

        Args:
            message: The user message
            user_id: The user ID
            session_id: The session ID
            api_auth_token: API authentication token information

        Yields:
            Dictionary with response details for each event
        """
        logger.info(f"Processing message for session {session_id}: {message[:50]}...")

        # Check if session exists and create it if it doesn't
        session = await self.get_session(user_id, session_id)

        print("session found: ", session.id)

        # Store API auth token in session state if provided
        if api_auth_token and session and hasattr(session, 'state'):
            print("store api auth token in session state")
            if not hasattr(session.state, '__setitem__'):
                # If state is not a dict, make it one
                session.state = {}
            session.state["api_auth_token"] = api_auth_token

        # Update active session tracking
        if session_id in self.active_sessions:
            print("update active session tracking")
            self.active_sessions[session_id]["last_activity"] = time.time()
        else:
            # Add to active sessions if not already tracked
            print("add to active sessions if not already tracked")
            self.active_sessions[session_id] = {
                "user_id": user_id,
                "created_at": time.time(),
                "last_activity": time.time()
            }

        # Prepare the user message
        content = types.Content(role='user', parts=[types.Part(text=message)])

        # Process through runner
        try:
            logger.info(f"Running message through runner {user_id}, {session_id}, {content}")
            async for event in self.runner.run_async(
                user_id=user_id,
                session_id=session_id,
                new_message=content
            ):
                # Extract text content if available
                text_content = None
                try:
                    if (hasattr(event, 'content') and event.content and
                        hasattr(event.content, 'parts') and event.content.parts and
                        len(event.content.parts) > 0 and
                        hasattr(event.content.parts[0], 'text') and event.content.parts[0].text):
                        text_content = event.content.parts[0].text
                except (AttributeError, IndexError, TypeError):
                    text_content = None


                # Safely get event type name
                try:
                    event_type = type(event).__name__
                except AttributeError:
                    event_type = "UnknownEvent"

                response = {
                    "type": event_type,
                    "author": getattr(event, 'author', 'unknown'),
                    "is_final": event.is_final_response() if hasattr(event, 'is_final_response') else False,
                    "timestamp": time.time()
                }


                if text_content:
                    response["content"] = text_content
                logger.info("finalizing event")
                if hasattr(event, 'is_final_response') and event.is_final_response():
                    print("final response found")
                    # Add user session data to final response
                    session = await self.get_session(user_id, session_id)
                    if session and hasattr(session, 'state') and session.state:
                        # Get relevant state info (excluding potentially large objects)
                        safe_state = {}
                        for key, value in session.state.items():
                            if key in ["last_ticket_id", "session_metadata"]:
                                safe_state[key] = value

                        if safe_state:
                            response["session_data"] = safe_state
                yield response
        except Exception as e:
            import traceback
            logger.error(f"Error processing message: {str(e)}")
            logger.error(f"Full traceback: {traceback.format_exc()}")

            # Check for specific error types and handle accordingly
            error_str = str(e)

            # Handle Google Gemini API overload (503 errors)
            if "503 UNAVAILABLE" in error_str and "overloaded" in error_str:
                logger.warning("Google Gemini API is overloaded, using direct API fallback")

                # Try to process the request directly using appropriate leave tools
                try:
                    # Import leave tools
                    from tools.leave_tools import request_leave_intelligent, get_pending_leaves, get_leave_balance, get_leave_history

                    # Create a simple tool context
                    class SimpleToolContext:
                        def __init__(self, session_state):
                            self.session_state = session_state or {}

                    # Get session state
                    session = await self.get_session(user_id, session_id)
                    session_state = session.state if session and hasattr(session, 'state') else {}
                    tool_context = SimpleToolContext(session_state)

                    # Determine the type of leave request and route accordingly
                    message_lower = message.lower()
                    logger.info(f"Attempting direct leave processing for message: {message}")

                    # Route to appropriate function based on message content
                    if any(keyword in message_lower for keyword in ['pending', 'show', 'view', 'list', 'display']):
                        if 'pending' in message_lower:
                            logger.info("Detected pending leaves request, calling get_pending_leaves")
                            result = get_pending_leaves(tool_context)
                        elif any(keyword in message_lower for keyword in ['balance', 'remaining', 'available']):
                            logger.info("Detected leave balance request, calling get_leave_balance")
                            result = get_leave_balance(tool_context)
                        elif any(keyword in message_lower for keyword in ['history', 'past', 'previous']):
                            logger.info("Detected leave history request, calling get_leave_history")
                            result = get_leave_history(tool_context=tool_context)
                        else:
                            # Default to pending leaves for general "show" requests
                            logger.info("General show request, defaulting to get_pending_leaves")
                            result = get_pending_leaves(tool_context)
                    else:
                        # For actual leave requests (apply, request, need, etc.)
                        logger.info("Detected leave request creation, calling request_leave_intelligent")
                        result = request_leave_intelligent(message, tool_context)

                    if result.get("status") == "success":
                        # Success! Create appropriate response based on request type
                        if 'pending' in message_lower:
                            # Pending leaves response
                            pending_leaves = result.get('pending_leaves', [])
                            if pending_leaves:
                                text_content = f"""✅ **Your Pending Leave Requests**

I found {len(pending_leaves)} pending leave request(s):

"""
                                for i, leave in enumerate(pending_leaves, 1):
                                    text_content += f"""**Request #{i}:**
- **Dates**: {leave.get('start_date', 'N/A')} to {leave.get('end_date', 'N/A')}
- **Type**: {leave.get('leave_type', 'N/A')}
- **Reason**: {leave.get('reason', 'N/A')}
- **Status**: {leave.get('status', 'Pending')}

"""
                                text_content += "*Note: Retrieved using backup system while main AI service is experiencing high demand.*"
                            else:
                                text_content = """✅ **No Pending Leave Requests**

You currently have no pending leave requests.

*Note: Retrieved using backup system while main AI service is experiencing high demand.*"""

                        elif any(keyword in message_lower for keyword in ['balance', 'remaining', 'available']):
                            # Leave balance response
                            text_content = f"""✅ **Your Leave Balance**

{result.get('message', 'Leave balance retrieved successfully')}

*Note: Retrieved using backup system while main AI service is experiencing high demand.*"""

                        elif any(keyword in message_lower for keyword in ['history', 'past', 'previous']):
                            # Leave history response
                            text_content = f"""✅ **Your Leave History**

{result.get('message', 'Leave history retrieved successfully')}

*Note: Retrieved using backup system while main AI service is experiencing high demand.*"""

                        else:
                            # Leave request creation response
                            text_content = f"""✅ **Leave Request Processed Successfully!**

I've successfully processed your leave request using our direct API integration:

**Leave Details:**
- **Date**: {result.get('start_date', 'Today')}
- **Type**: {result.get('leave_type', 'Sick Leave').title()}
- **Reason**: {result.get('reason', 'Not feeling well')}
- **Status**: {result.get('status', 'Submitted')}

**Request ID**: {result.get('leave_id', 'Generated')}

Your leave request has been submitted to the system. You should receive a confirmation email shortly.

*Note: I processed this request using our backup system while the main AI service is experiencing high demand.*"""
                    else:
                        # Failed to process, provide helpful guidance
                        text_content = f"""I apologize, but the AI service is currently experiencing high demand. I attempted to process your leave request directly but encountered an issue:

**Error**: {result.get('message', 'Unable to process request')}

**Alternative Options:**
1. 🔄 Try again in a few minutes when the AI service recovers
2. 📞 Contact HR <NAME_EMAIL>
3. 🌐 Use the WagonHR portal directly
4. 📧 Send an email to your manager with your leave request

**Your Request**: "{message}"

I understand you need sick leave for today. Please try one of the alternative options above."""

                    response = {
                        "type": "DirectAPIResponse",
                        "author": "hr_assistant",
                        "is_final": True,
                        "timestamp": time.time(),
                        "content": text_content
                    }

                    logger.info("Yielding direct API response due to LLM overload")
                    yield response

                except Exception as direct_api_error:
                    logger.error(f"Direct API fallback also failed: {str(direct_api_error)}")

                    # Final fallback - provide helpful guidance
                    text_content = f"""I apologize, but both our main AI service and backup systems are currently experiencing issues.

**Your Request**: "{message}"

**Immediate Actions You Can Take:**
1. 📞 **Call HR directly**: Contact your HR representative
2. 📧 **Email your manager**: Send your leave request directly
3. 🌐 **Use WagonHR portal**: Log in directly to submit your request
4. 🔄 **Try again later**: The service should recover shortly

**For Urgent Leave Requests:**
- Contact your immediate supervisor
- Send an <NAME_EMAIL>
- Call the main office number

I understand this is inconvenient, and I apologize for the technical difficulties."""

                    response = {
                        "type": "FallbackGuidance",
                        "author": "hr_assistant",
                        "is_final": True,
                        "timestamp": time.time(),
                        "content": text_content
                    }

                    logger.info("Yielding final fallback guidance")
                    yield response

            # If there's an error with the API key, use mock data
            elif "API key not valid" in error_str:
                logger.warning("API key not valid, using mock data instead")
                os.environ["USE_MOCK_DATA"] = "true"

                # Create a mock event with a more helpful response
                text_content = "I'm operating in offline mode due to API key issues, but I can still help with basic HR inquiries. I can provide information about leave balances, leave applications, attendance records, and HR policies. How can I assist you today?"

                response = {
                    "type": "MockEvent",
                    "author": "mock_agent",
                    "is_final": True,
                    "timestamp": time.time(),
                    "content": text_content
                }

                logger.info("Yielding mock response due to API key error")
                yield response
            else:
                # For other errors, return an error response
                yield {
                    "type": "error",
                    "message": f"Error processing message: {str(e)}",
                    "timestamp": time.time()
                }

    def get_active_sessions(self) -> Dict[str, Dict[str, Any]]:
        """Get all active sessions.

        Returns:
            Dictionary of active sessions
        """
        return self.active_sessions.copy()

    def get_session_events(self, session_id: str) -> List[Dict[str, Any]]:
        """Get events for a specific session.

        Args:
            session_id: The session ID

        Returns:
            List of events for the session
        """
        return self.session_service.get_session_events(session_id)

