version: '3.8'

services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**************************************/postgres
      - ENVIRONMENT=dev
      - LOG_LEVEL=INFO
      - GOOGLE_GENAI_USE_VERTEXAI=${GOOGLE_GENAI_USE_VERTEXAI:-False}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - ROOT_MODEL=${ROOT_MODEL:-gemini-2.0-flash}
      - SUB_AGENT_MODEL=${SUB_AGENT_MODEL:-gemini-2.0-flash}
      # AWS specific environment variables
      - AWS_REGION=${AWS_REGION:-us-east-1}
    volumes:
      - .:/app
    depends_on:
      - db
    restart: unless-stopped

  db:
    image: postgres:14
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=postgres
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped

volumes:
  postgres_data:
