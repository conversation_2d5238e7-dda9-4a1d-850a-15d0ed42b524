#!/usr/bin/env python3
"""
Test script to verify the API is working without authentication.
This tests the core functionality without requiring a valid Microsoft token.
"""

import requests
import json
import time

def test_api_without_auth():
    """Test the API with a simple message without authentication."""
    
    print("🧪 Testing API without Authentication")
    print("=" * 50)
    
    # Check if server is running
    try:
        response = requests.get("http://localhost:8001/")
        if response.status_code == 200:
            print("✅ API server is running")
        else:
            print(f"❌ API server returned status {response.status_code}")
            return
    except requests.exceptions.ConnectionError:
        print("❌ API server is not running. Please start it with: python api_server.py")
        return
    
    # Test the message endpoint without authentication
    print("\n🚀 Testing Message API without Authentication")
    print("=" * 50)
    
    url = "http://localhost:8001/message"
    payload = {
        "message": "Hello, can you help me with leave information?",
        "user_id": "test_user",
        "session_id": "test_session"
        # Note: No access_token provided
    }
    
    print(f"📋 Request Details:")
    print(f"   URL: {url}")
    print(f"   Method: POST")
    print(f"   Payload: {json.dumps(payload, indent=2)}")
    
    try:
        print(f"\n🔄 Making API request...")
        response = requests.post(
            url,
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"📋 Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ API call successful!")
            try:
                response_data = response.json()
                print(f"📄 Response: {json.dumps(response_data, indent=2)}")
            except json.JSONDecodeError:
                print(f"📄 Response (raw): {response.text}")
        else:
            print(f"❌ API call failed with status {response.status_code}")
            try:
                error_data = response.json()
                print(f"📄 Error response: {json.dumps(error_data, indent=2)}")
            except json.JSONDecodeError:
                print(f"📄 Error response (raw): {response.text}")
                
    except requests.exceptions.Timeout:
        print("⏰ Request timed out after 30 seconds")
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
    
    print(f"\n🎉 Test completed!")
    print("💡 This test verifies that the core API functionality is working")

if __name__ == "__main__":
    test_api_without_auth()
