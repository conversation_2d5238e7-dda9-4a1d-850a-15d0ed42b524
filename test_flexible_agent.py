#!/usr/bin/env python3
"""
Test script for the Flexible WagonHR API Agent.

This script demonstrates how to use the flexible agent that can call any WagonHR API
by providing endpoint details dynamically without hardcoding.
"""

import sys
import os
import logging

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_predefined_endpoints():
    """Test the predefined endpoints in the flexible agent."""
    try:
        logger.info("=== TESTING PREDEFINED ENDPOINTS ===")
        
        from tools.flexible_wagonhr_agent import process_wagonhr_request
        
        # Test cases for predefined endpoints
        test_cases = [
            "How many leave days do I have left?",
            "I want to apply for casual leave from 2024-02-15 to 2024-02-17 because I'm sick",
            "Show me my leave history for last month",
            "Record my attendance as present today",
            "Generate my attendance report for January 2024",
            "Show me my employee profile information"
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            logger.info(f"\n--- Test Case {i}: {test_case} ---")
            result = process_wagonhr_request(test_case)
            logger.info(f"Result: {result}")
        
        logger.info("=== PREDEFINED ENDPOINTS TEST COMPLETED ===")
        
    except Exception as e:
        logger.error(f"Error testing predefined endpoints: {e}")
        import traceback
        traceback.print_exc()

def test_custom_api_calls():
    """Test custom API calls with endpoint details provided on-the-fly."""
    try:
        logger.info("=== TESTING CUSTOM API CALLS ===")
        
        from tools.flexible_wagonhr_agent import call_custom_api
        
        # Test Case 1: Custom leave approval endpoint
        logger.info("\n--- Custom Test 1: Leave Approval ---")
        leave_approval_endpoint = {
            "path": "/api/hrms/leave/approve",
            "method": "PUT",
            "description": "Approve a leave request",
            "parameters": ["requestId", "status", "comments"]
        }
        
        result1 = call_custom_api(
            leave_approval_endpoint,
            "Approve leave request ID 12345 with status approved and comments 'Approved by manager'"
        )
        logger.info(f"Leave Approval Result: {result1}")
        
        # Test Case 2: Custom timesheet submission
        logger.info("\n--- Custom Test 2: Timesheet Submission ---")
        timesheet_endpoint = {
            "path": "/api/hrms/timesheet/submit",
            "method": "POST",
            "description": "Submit employee timesheet",
            "parameters": ["date", "hoursWorked", "projectCode", "description"]
        }
        
        result2 = call_custom_api(
            timesheet_endpoint,
            "Submit timesheet for 2024-02-15 with 8 hours worked on project ABC123 for development work"
        )
        logger.info(f"Timesheet Submission Result: {result2}")
        
        # Test Case 3: Custom employee search
        logger.info("\n--- Custom Test 3: Employee Search ---")
        employee_search_endpoint = {
            "path": "/api/hrms/employee/search",
            "method": "GET",
            "description": "Search for employees",
            "parameters": ["query", "department", "limit"]
        }
        
        result3 = call_custom_api(
            employee_search_endpoint,
            "Search for employees in IT department with query 'John' and limit 10"
        )
        logger.info(f"Employee Search Result: {result3}")
        
        # Test Case 4: Custom policy retrieval
        logger.info("\n--- Custom Test 4: Policy Retrieval ---")
        policy_endpoint = {
            "path": "/api/hrms/policy/get",
            "method": "GET",
            "description": "Get HR policy information",
            "parameters": ["policyType", "version"]
        }
        
        result4 = call_custom_api(
            policy_endpoint,
            "Get leave policy information for version 2024"
        )
        logger.info(f"Policy Retrieval Result: {result4}")
        
        logger.info("=== CUSTOM API CALLS TEST COMPLETED ===")
        
    except Exception as e:
        logger.error(f"Error testing custom API calls: {e}")
        import traceback
        traceback.print_exc()

def test_add_custom_endpoints():
    """Test adding custom endpoints to the flexible agent."""
    try:
        logger.info("=== TESTING ADD CUSTOM ENDPOINTS ===")
        
        from tools.flexible_wagonhr_agent import add_custom_endpoint, process_wagonhr_request
        
        # Add a custom endpoint for salary information
        logger.info("\n--- Adding Custom Salary Endpoint ---")
        salary_endpoint = {
            "path": "/api/hrms/salary/details",
            "method": "GET",
            "description": "Get employee salary details",
            "parameters": ["month", "year", "includeDeductions"]
        }
        
        add_result = add_custom_endpoint("salary_details", salary_endpoint)
        logger.info(f"Add Endpoint Result: {add_result}")
        
        # Test using the newly added endpoint
        if add_result.get("status") == "success":
            logger.info("\n--- Testing Custom Salary Endpoint ---")
            salary_result = process_wagonhr_request("Show me my salary details for January 2024")
            logger.info(f"Salary Query Result: {salary_result}")
        
        # Add another custom endpoint for performance reviews
        logger.info("\n--- Adding Custom Performance Review Endpoint ---")
        performance_endpoint = {
            "path": "/api/hrms/performance/review",
            "method": "POST",
            "description": "Submit performance review",
            "parameters": ["revieweeId", "rating", "comments", "goals"]
        }
        
        add_result2 = add_custom_endpoint("performance_review", performance_endpoint)
        logger.info(f"Add Performance Endpoint Result: {add_result2}")
        
        logger.info("=== ADD CUSTOM ENDPOINTS TEST COMPLETED ===")
        
    except Exception as e:
        logger.error(f"Error testing add custom endpoints: {e}")
        import traceback
        traceback.print_exc()

def demonstrate_flexible_usage():
    """Demonstrate various ways to use the flexible agent."""
    try:
        logger.info("=== DEMONSTRATING FLEXIBLE USAGE ===")
        
        from tools.flexible_wagonhr_agent import call_custom_api, flexible_wagonhr_agent
        
        # Show available predefined endpoints
        logger.info("\n--- Available Predefined Endpoints ---")
        for key, endpoint in flexible_wagonhr_agent.api_endpoints.items():
            logger.info(f"  {key}: {endpoint['method']} {endpoint['path']} - {endpoint['description']}")
        
        # Demonstrate different parameter extraction scenarios
        logger.info("\n--- Parameter Extraction Examples ---")
        
        # Example 1: Date range extraction
        test_endpoint = {
            "path": "/api/hrms/reports/custom",
            "method": "GET",
            "description": "Generate custom report",
            "parameters": ["startDate", "endDate", "reportType"]
        }
        
        date_examples = [
            "Generate report from 2024-01-01 to 2024-01-31",
            "Show data from January 1st 2024 to January 31st 2024",
            "Get report for 01/01/2024 to 01/31/2024"
        ]
        
        for example in date_examples:
            logger.info(f"\nTesting: {example}")
            result = call_custom_api(test_endpoint, example)
            logger.info(f"Extracted parameters: {result.get('endpoint_details', {})}")
        
        # Example 2: Complex parameter extraction
        complex_endpoint = {
            "path": "/api/hrms/leave/bulk-request",
            "method": "POST",
            "description": "Submit bulk leave request",
            "parameters": ["startDate", "endDate", "leaveType", "reason", "halfDay", "location"]
        }
        
        complex_request = "Apply for casual half-day leave from 2024-03-15 to 2024-03-16 because of medical appointment at downtown clinic"
        logger.info(f"\nComplex Request: {complex_request}")
        complex_result = call_custom_api(complex_endpoint, complex_request)
        logger.info(f"Complex Result: {complex_result}")
        
        logger.info("=== FLEXIBLE USAGE DEMONSTRATION COMPLETED ===")
        
    except Exception as e:
        logger.error(f"Error demonstrating flexible usage: {e}")
        import traceback
        traceback.print_exc()

def show_usage_examples():
    """Show practical usage examples for developers."""
    logger.info("=== USAGE EXAMPLES FOR DEVELOPERS ===")
    
    logger.info("""
    
    # Example 1: Using predefined endpoints
    from tools.flexible_wagonhr_agent import process_wagonhr_request
    
    result = process_wagonhr_request("How many leave days do I have?")
    print(result)
    
    # Example 2: Custom API call with endpoint details
    from tools.flexible_wagonhr_agent import call_custom_api
    
    endpoint_details = {
        "path": "/api/hrms/custom/endpoint",
        "method": "POST",
        "description": "Custom operation",
        "parameters": ["param1", "param2"]
    }
    
    result = call_custom_api(endpoint_details, "User request with param1 value and param2 value")
    print(result)
    
    # Example 3: Adding permanent custom endpoints
    from tools.flexible_wagonhr_agent import add_custom_endpoint
    
    custom_config = {
        "path": "/api/hrms/new/feature",
        "method": "GET",
        "description": "New feature endpoint",
        "parameters": ["feature_param"]
    }
    
    add_custom_endpoint("new_feature", custom_config)
    
    # Now you can use it with process_wagonhr_request
    result = process_wagonhr_request("Use new feature with some parameter")
    print(result)
    
    """)

if __name__ == "__main__":
    logger.info("Starting Flexible WagonHR Agent Tests...")
    
    # Show usage examples first
    show_usage_examples()
    print("\n" + "="*50 + "\n")
    
    # Run all tests
    test_predefined_endpoints()
    print("\n" + "="*50 + "\n")
    
    test_custom_api_calls()
    print("\n" + "="*50 + "\n")
    
    test_add_custom_endpoints()
    print("\n" + "="*50 + "\n")
    
    demonstrate_flexible_usage()
    
    logger.info("All flexible agent tests completed!")
