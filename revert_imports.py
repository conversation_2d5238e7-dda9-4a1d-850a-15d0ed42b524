#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to revert import statements in Python files.
This script replaces 'from ' with 'from ' in all Python files
where appropriate.
"""

import os
import re

def revert_imports_in_file(file_path):
    """Revert import statements in a single file."""
    # Skip files in the venv directory
    if '/venv/' in file_path:
        return
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Replace 'from agents.' with 'from agents.'
    # Replace 'from runners.' with 'from runners.'
    # Replace 'from services.' with 'from services.'
    # Replace 'from utils.' with 'from utils.'
    # Replace 'from tools.' with 'from tools.'
    
    patterns = [
        (r'from agents\.', 'from agents.'),
        (r'from runners\.', 'from runners.'),
        (r'from services\.', 'from services.'),
        (r'from utils\.', 'from utils.'),
        (r'from tools\.', 'from tools.')
    ]
    
    updated_content = content
    for pattern, replacement in patterns:
        updated_content = re.sub(pattern, replacement, updated_content)
    
    # Only write to the file if changes were made
    if updated_content != content:
        with open(file_path, 'w') as f:
            f.write(updated_content)
        print(f"Updated imports in {file_path}")
    else:
        print(f"No changes needed in {file_path}")

def find_python_files(directory):
    """Find all Python files in the given directory and its subdirectories."""
    python_files = []
    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith('.py'):
                python_files.append(os.path.join(root, file))
    return python_files

def main():
    """Main function to revert imports in all Python files."""
    # Get the current directory
    current_dir = os.getcwd()
    
    # Find all Python files
    python_files = find_python_files(current_dir)
    
    # Update imports in each file
    for file_path in python_files:
        revert_imports_in_file(file_path)
    
    print(f"Processed {len(python_files)} files")

if __name__ == "__main__":
    main()
