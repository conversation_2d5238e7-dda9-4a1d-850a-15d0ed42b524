"""
<PERSON><PERSON><PERSON> to run the HR policy vectorization process.

This script creates the necessary tables and vectorizes all HR policies.
"""

import os
import sys
import logging
import time

from utils.env_setup import setup_environment
from utils.logging_config import configure_logging

# Configure logging
configure_logging()
logger = logging.getLogger(__name__)

# Set up environment
if not setup_environment():
    logger.error("Failed to set up environment. Please check your .env file and API keys.")
    sys.exit(1)

def main():
    """Run the HR policy vectorization process."""
    try:
        logger.info("Starting HR policy vectorization process")
        
        # Create policy vector tables
        logger.info("Creating policy vector tables")
        from scripts.create_policy_vector_tables import create_policy_vector_tables
        if not create_policy_vector_tables():
            logger.error("Failed to create policy vector tables")
            return False
        
        # Vectorize policies
        logger.info("Vectorizing HR policies")
        from scripts.vectorize_policies import vectorize_policies
        if not vectorize_policies():
            logger.error("Failed to vectorize HR policies")
            return False
        
        logger.info("HR policy vectorization process completed successfully")
        return True
    except Exception as e:
        logger.error(f"Error running HR policy vectorization process: {str(e)}")
        return False

if __name__ == "__main__":
    if main():
        logger.info("HR policy vectorization process completed successfully")
    else:
        logger.error("Failed to run HR policy vectorization process")
        sys.exit(1)
