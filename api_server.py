"""
API Server for HR AI Assistant Solution.

This module provides a FastAPI server for the HR AI solution.
"""

import asyncio
import logging
import os
import jwt
import base64
import requests
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone
import json

from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks, Request, Header
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, StreamingResponse
from pydantic import BaseModel, Field
# from sse_starlette.sse import EventSourceResponse

from main import ItsmsApplication as HRApplication
from utils.env_setup import setup_environment
from utils.logging_config import configure_logging
from utils.db_config import init_db
from utils.hr_auth import hr_auth
from api.audit_routes import router as audit_router

# Configure logging
configure_logging()
logger = logging.getLogger(__name__)

# Set up environment
if not setup_environment():
    logger.error("Failed to set up environment. Please check your .env file and API keys.")
    exit(1)

# Initialize database
try:
    init_db()
    logger.info("Database initialized successfully")
except Exception as e:
    logger.error(f"Error initializing database: {str(e)}")
    exit(1)

# Create FastAPI app
app = FastAPI(
    title="Virtual HR AI Assistant API",
    description="API for the Virtual HR AI Assistant",
    version="0.1.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

# Create ITSM application
hr_app = HRApplication(
    app_name="itsm_api",
    root_model=os.environ.get("ROOT_MODEL", "gemini-2.0-flash"),
    sub_agent_model=os.environ.get("SUB_AGENT_MODEL", "gemini-2.0-flash"),
    safety_callbacks=True,
    log_level=logging.INFO
)

# Include routers
app.include_router(audit_router)

# Models
class SessionRequest(BaseModel):
    """Session creation request model."""
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    initial_state: Optional[Dict[str, Any]] = None

class MessageRequest(BaseModel):
    """Message request model."""
    message: str
    user_id: str
    session_id: str
    token: Optional[str] = None

class MessageResponse(BaseModel):
    """Message response model."""
    content: Optional[str] = None
    type: str = "message"
    is_final: bool = True
    error: Optional[str] = None

# Add new model for Masi orchestrator request
class OrchestratorRequest(BaseModel):
    """Request model for Masi orchestrator."""
    conversation_id: Optional[str] = None
    user_id: str
    question: str

class PolicySearchRequest(BaseModel):
    """Model for policy search request."""
    query: str
    top_k: int = 5

class PolicyByIdRequest(BaseModel):
    """Model for policy by ID request."""
    policy_id: str

class PolicyByCategoryRequest(BaseModel):
    """Model for policy by category request."""
    category: str


class AttendanceRecordRequest(BaseModel):
    """Model for attendance record request."""
    employee_email: str
    status: str
    timestamp: Optional[str] = None
    location: Optional[Dict[str, float]] = None
    notes: Optional[str] = None

class AttendanceReportRequest(BaseModel):
    """Model for attendance report request."""
    employee_email: str
    start_date: str
    end_date: str

class AttendanceAnomalyRequest(BaseModel):
    """Model for attendance anomaly detection request."""
    employee_email: Optional[str] = None
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    anomaly_types: Optional[List[str]] = None


def get_api_auth_token(access_token: str) -> Dict[str, Any]:
    """
    Extract and validate API authentication token from Microsoft access token.

    Args:
        access_token (str): Microsoft access token from frontend

    Returns:
        Dict[str, Any]: Dictionary containing token information and validation status

    Raises:
        HTTPException: If token is invalid or expired
    """
    print("get api Access token: ", access_token)
    if not access_token:
        logger.error("No access token provided")
        raise HTTPException(status_code=401, detail="Access token is required")

    try:
        # Remove 'Bearer ' prefix if present
        if access_token.startswith('Bearer '):
            access_token = access_token[7:]

        # Call WagonHR auth API to exchange Microsoft token for API auth token
        try:
            # Call WagonHR auth token API which takes this token and type as request body and returns an auth token
            url = "https://dev-api-wagonhr.mouritech.net/auth/user/v3/login"
            body = json.dumps({"accessToken": access_token, "type": "azure_ad_identifier"})
            response = requests.post(url, data=body, headers={"Content-Type": "application/json"})

            if response.status_code == 200:
                token_data = response.json()
                # Extract user information from the response
                data = token_data.get("data")
                api_auth_token = data.get("user").get("token")

                # Set both Microsoft and WagonHR tokens in the HR auth manager for API calls
                if hr_auth:
                    logger.info("Setting Microsoft token in HR auth manager...")
                    hr_auth.set_microsoft_token(
                        token=access_token,
                        expiry_timestamp=token_data.get("expires_at")
                    )
                    logger.info("Microsoft token set in HR auth manager")

                    # Set the WagonHR token obtained from the exchange
                    logger.info(f"Setting WagonHR token in HR auth manager: {api_auth_token[:20]}...")
                    try:
                        hr_auth.set_wagonhr_token(
                            token=api_auth_token,
                            expiry_timestamp=token_data.get("expires_at")
                        )
                        logger.info("WagonHR token set in HR auth manager successfully")
                    except Exception as e:
                        logger.error(f"Failed to set WagonHR token: {str(e)}")
                else:
                    logger.error("hr_auth is None - cannot set tokens")

                # Set tenant/entity context for API clients
                user_data = data.get("user", {})
                entity_permissions = user_data.get("entityPermissions", [])
                if entity_permissions:
                    # Use the default entity (first one or the one marked as default)
                    default_entity = None
                    for entity in entity_permissions:
                        if entity.get("isDefault", False):
                            default_entity = entity
                            break
                    if not default_entity and entity_permissions:
                        default_entity = entity_permissions[0]

                    if default_entity:
                        entity_code = default_entity.get("legalEntityCode")
                        entity_name = default_entity.get("legalEntityName")
                        logger.info(f"Setting tenant context: Entity={entity_name} ({entity_code})")

                        # Import and set context for API clients
                        from utils.hr_api_client import leave_balance_client, attendance_client
                        if entity_code:
                            # Set tenant_id to 'mouritech' and entity_id to the entity code
                            leave_balance_client.set_tenant_context(
                                tenant_id="mouritech",
                                entity_id=entity_code
                            )
                            attendance_client.set_tenant_context(
                                tenant_id="mouritech",
                                entity_id=entity_code
                            )
                            logger.info("Tenant/entity context set for API clients")

                return api_auth_token
            else:
                error_text = response.text
                logger.error(f"Failed to get API auth token: {error_text}")

                # Check if it's a JWT expiry error
                if "Jwt expired" in error_text or "expired" in error_text.lower():
                    raise HTTPException(
                        status_code=401,
                        detail="Your session has expired. Please log in again to continue."
                    )
                else:
                    raise HTTPException(
                        status_code=401,
                        detail="Failed to authenticate with WagonHR API. Please try logging in again."
                    )
        except requests.RequestException as e:
            logger.error(f"Failed to call WagonHR API: {str(e)}")
            raise HTTPException(status_code=500, detail="Failed to connect to WagonHR API")

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error processing access token: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to process access token")


# Routes
@app.get("/")
async def root():
    """Root endpoint."""
    return {"message": "Welcome to the HR AI Service Desk API"}

@app.get("/health")
async def health_check():
    """
    Simple health check endpoint for monitoring and deployment.

    Returns:
        Dict[str, Any]: Simple health status with version and docs info
    """
    try:
        # Check critical services quickly
        is_healthy = True

        # Quick HR application check
        try:
            if not (hr_app and hasattr(hr_app, 'runner_service')):
                is_healthy = False
        except Exception:
            is_healthy = False

        # Simple response format with dynamic version (similar to Java's getImplementationVersion)
        def get_package_version():
            try:
                # Python equivalent of Java's getImplementationVersion()
                from importlib.metadata import version, PackageNotFoundError
                return f"v{version('hr-solution')}"  # Replace with your actual package name
            except (PackageNotFoundError, ImportError):
                pass

            try:
                # Alternative: try importlib_metadata for older Python versions
                from importlib_metadata import version, PackageNotFoundError
                return f"v{version('hr-solution')}"
            except (PackageNotFoundError, ImportError):
                pass

            try:
                # Try to get version from __version__ attribute in main module
                import __main__
                if hasattr(__main__, '__version__'):
                    return f"v{__main__.__version__}"
            except:
                pass

            # Fallback to environment variable
            if os.environ.get("API_VERSION"):
                return os.environ.get("API_VERSION")

            # Fallback to VERSION file
            try:
                with open("VERSION", "r") as f:
                    return f.read().strip()
            except FileNotFoundError:
                pass

            # Final fallback: timestamp-based version
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y.%m.%d")
            return f"v{timestamp}"

        api_version = get_package_version()
        response_data = {
            "version": api_version,
            "docs": {
                "url": "/docs",
                "status": "Live" if is_healthy else "Degraded"
            }
        }

        # Return appropriate status code
        status_code = 200 if is_healthy else 503

        return JSONResponse(
            content=response_data,
            status_code=status_code
        )

    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        # Use same package version logic for error response
        def get_package_version():
            try:
                from importlib.metadata import version, PackageNotFoundError
                return f"v{version('hr-solution')}"
            except (PackageNotFoundError, ImportError):
                pass
            if os.environ.get("API_VERSION"):
                return os.environ.get("API_VERSION")
            try:
                with open("VERSION", "r") as f:
                    return f.read().strip()
            except FileNotFoundError:
                pass
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y.%m.%d")
            return f"v{timestamp}"

        api_version = get_package_version()
        return JSONResponse(
            content={
                "version": api_version,
                "docs": {
                    "url": "/docs",
                    "status": "Error"
                }
            },
            status_code=503
        )

@app.post("/test-pending-leaves")
async def test_pending_leaves(request: Request):
    """Test endpoint to directly call get_pending_leaves function bypassing AI"""
    try:
        # Import the function directly
        from tools.leave_tools import get_pending_leaves

        # Set up authentication headers from request
        auth_token = request.headers.get("Authorization", "").replace("Bearer ", "")
        tenant_id = request.headers.get("x-tenantid", "mouritech")
        entity_id = request.headers.get("x-entityid", "US-MT-002")

        print(f"Test endpoint - Auth token: {auth_token[:50]}...")
        print(f"Test endpoint - Tenant: {tenant_id}, Entity: {entity_id}")

        # Set up HR auth context
        from utils.hr_auth import hr_auth
        hr_auth.set_microsoft_token(auth_token)
        hr_auth.set_wagonhr_token(get_api_auth_token(auth_token))

        from utils.hr_api_client import leave_balance_client
        leave_balance_client.set_tenant_context(tenant_id, entity_id)

        print("Test endpoint - Calling get_pending_leaves directly...")

        # Call the function directly
        result = get_pending_leaves()

        print(f"Test endpoint - Result: {result}")

        return {
            "test_result": "success",
            "function_called": "get_pending_leaves",
            "result": result
        }

    except Exception as e:
        print(f"Test endpoint error: {str(e)}")
        return {
            "test_result": "error",
            "error": str(e)
        }

@app.get("/test-date-parsing")
async def test_date_parsing():
    """Test endpoint to verify date parsing works correctly."""
    try:
        print("Test date parsing - Testing date calculation logic...")

        # Test the date calculation logic directly
        from datetime import datetime, timedelta

        # Get current date for context
        current_date = datetime.now().strftime("%Y-%m-%d")
        current_day = datetime.now().strftime("%A")
        tomorrow_date = (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d")

        print(f"Current date: {current_date} ({current_day})")
        print(f"Tomorrow date: {tomorrow_date}")

        # Test different date expressions
        test_results = {
            "current_date": current_date,
            "current_day": current_day,
            "tomorrow_date": tomorrow_date,
            "date_calculation_test": {
                "today": current_date,
                "tomorrow": tomorrow_date,
                "next_week": (datetime.now() + timedelta(days=7)).strftime("%Y-%m-%d")
            }
        }

        # Test the prompt template
        parsing_prompt = f"""
You are an intelligent HR assistant that parses natural language leave requests into structured data.

Current date: {current_date} ({current_day})

Parse the following leave request and extract the information in JSON format:

Leave Request: "I need sick leave for tomorrow"

Extract the following information:
1. start_date: The start date in YYYY-MM-DD format
2. end_date: The end date in YYYY-MM-DD format (same as start_date if single day)
3. leave_type: Type of leave (sick, annual, personal, casual, maternity, paternity, bereavement, emergency)
4. reason: The reason for the leave
5. half_day: Boolean indicating if it's a half-day leave
6. interpretation: A human-readable interpretation of what was understood

Rules for date parsing:
- "next Tuesday" means the Tuesday of next week
- "coming Tuesday" means the next Tuesday (could be this week or next week)
- "tomorrow" means {tomorrow_date}
- "today" means {current_date}
- For day names without qualifiers, assume the next occurrence of that day

Example response:
{{
    "success": true,
    "start_date": "2025-06-04",
    "end_date": "2025-06-04",
    "leave_type": "sick",
    "reason": "Not feeling well",
    "half_day": false,
    "interpretation": "Requesting sick leave on Tuesday, June 4th, 2025 - Not feeling well"
}}

Return only valid JSON.
"""

        test_results["prompt_template"] = parsing_prompt

        return {"status": "success", "test_results": test_results}

    except Exception as e:
        print(f"Test date parsing error: {e}")
        return {"message": f"Test date parsing error: {str(e)}"}

@app.post("/sessions", response_model=Dict[str, str])
async def create_session(request: SessionRequest):
    """Create a new session."""
    try:
        return await hr_app.create_session(
            user_id=request.user_id,
            session_id=request.session_id,
            initial_state=request.initial_state
        )
    except Exception as e:
        logger.error(f"Error creating session: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/sessions", response_model=Dict[str, Dict[str, Any]])
async def get_sessions():
    """Get all active sessions."""
    try:
        return await hr_app.get_active_sessions()
    except Exception as e:
        logger.error(f"Error getting sessions: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/message", response_model=MessageResponse)
async def process_message(
    request: MessageRequest,
    authorization: Optional[str] = Header(None)
):
    """Process a message and return the final response."""
    print("intial request from frontend: ", request)
    try:
        # Get access token from request body or headers
        access_token = request.token
        print("FE Access token: ", access_token)
        if not access_token and authorization:
            access_token = authorization.replace("Bearer ", "")

        if not access_token:
            raise HTTPException(status_code=401, detail="Access token is required")

        # Get API Auth token from access_token and validate it
        print("get the access token from api token")
        api_auth_token = get_api_auth_token(access_token)

        print("API Auth token: ", api_auth_token)

        # Extract user information from token if not provided
        if not request.user_id and api_auth_token.get("user_id"):
            request.user_id = api_auth_token["user_id"]

        print("request after extracting user id: ", request)
        print("send process message to hr app")
        response = await hr_app.process_message(
            message=request.message,
            user_id=request.user_id,
            session_id=request.session_id,
            api_auth_token=api_auth_token
        )

        print("response from process message: ", response)

        if response.get("type") == "error":
            return MessageResponse(
                type="error",
                error=response.get("message", "Unknown error"),
                is_final=True
            )

        return MessageResponse(
            content=response.get("content"),
            type=response.get("type", "message"),
            is_final=response.get("is_final", True)
        )
    except HTTPException:
        # Re-raise HTTP exceptions (like 401 Unauthorized)
        raise
    except Exception as e:
        logger.error(f"Error processing message: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# @app.post("/message/stream")
# async def process_message_stream(request: MessageRequest):
#     """Process a message and stream the responses."""
#     async def event_generator():
#         try:
#             async for event in hr_app.process_message_stream(
#                 message=request.message,
#                 user_id=request.user_id,
#                 session_id=request.session_id
#             ):
#                 if event.get("type") == "error":
#                     yield {
#                         "event": "error",
#                         "data": event.get("message", "Unknown error")
#                     }
#                     return
#
#                 yield {
#                     "event": "message",
#                     "data": event.get("content", ""),
#                     "id": event.get("id", ""),
#                     "retry": 1000
#                 }
#
#                 if event.get("is_final", False):
#                     yield {
#                         "event": "done",
#                         "data": ""
#                     }
#         except Exception as e:
#             logger.error(f"Error streaming message: {str(e)}")
#             yield {
#                 "event": "error",
#                 "data": str(e)
#             }
#
#     # return EventSourceResponse(event_generator())
#     return JSONResponse({"message": "Streaming not available"})


# Policy Search Routes
@app.post("/policy/search", response_model=Dict[str, Any])
async def search_policies(request: PolicySearchRequest):
    """Search for HR policies."""
    try:
        from tools.policy_search_tools import search_hr_policies

        result = search_hr_policies(
            query=request.query,
            top_k=request.top_k
        )

        return result
    except Exception as e:
        logger.error(f"Error searching policies: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/policy/by-id", response_model=Dict[str, Any])
async def get_policy_by_id(request: PolicyByIdRequest):
    """Get an HR policy by ID."""
    try:
        from tools.policy_search_tools import get_hr_policy_by_id

        result = get_hr_policy_by_id(
            policy_id=request.policy_id
        )

        return result
    except Exception as e:
        logger.error(f"Error getting policy by ID: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/policy/by-category", response_model=Dict[str, Any])
async def get_policies_by_category(request: PolicyByCategoryRequest):
    """Get HR policies by category."""
    try:
        from tools.policy_search_tools import get_hr_policies_by_category

        result = get_hr_policies_by_category(
            category=request.category
        )

        return result
    except Exception as e:
        logger.error(f"Error getting policies by category: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Attendance Management Routes
@app.post("/attendance/record", response_model=Dict[str, Any])
async def record_attendance(request: AttendanceRecordRequest):
    """Record attendance for an employee."""
    try:
        from tools.attendance_tools import record_attendance as attendance_record_tool

        result = attendance_record_tool(
            employee_email=request.employee_email,
            status=request.status,
            timestamp=request.timestamp,
            location=request.location,
            notes=request.notes
        )

        return result
    except Exception as e:
        logger.error(f"Error recording attendance: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/attendance/report", response_model=Dict[str, Any])
async def get_attendance_report(request: AttendanceReportRequest):
    """Get attendance report for an employee."""
    try:
        from tools.attendance_tools import get_attendance_report as attendance_report_tool

        result = attendance_report_tool(
            employee_email=request.employee_email,
            start_date=request.start_date,
            end_date=request.end_date
        )

        return result
    except Exception as e:
        logger.error(f"Error getting attendance report: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/attendance/anomalies", response_model=Dict[str, Any])
async def get_attendance_anomalies(request: AttendanceAnomalyRequest):
    """Detect attendance anomalies."""
    try:
        from tools.attendance_tools import get_attendance_anomalies as attendance_anomaly_tool

        result = attendance_anomaly_tool(
            employee_email=request.employee_email,
            start_date=request.start_date,
            end_date=request.end_date,
            anomaly_types=request.anomaly_types
        )

        return result
    except Exception as e:
        logger.error(f"Error detecting attendance anomalies: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "api_server:app",
        host="0.0.0.0",
        port=int(os.environ.get("PORT", 8081)),
        reload=False
    )
