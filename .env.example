# HR Agent Configuration

# Environment Configuration
ENVIRONMENT=dev  # dev, staging, prod

# Google API Key - Required for Google ADK
GOOGLE_API_KEY=your-google-api-key-here
GOOGLE_GENAI_USE_VERTEXAI=False

# AWS Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-access-key-id
AWS_SECRET_ACCESS_KEY=your-secret-access-key

# Database Configuration
DATABASE_URL=*****************************************************/postgres

# Model Configuration
ROOT_MODEL=gemini-2.0-flash
SUB_AGENT_MODEL=gemini-2.0-flash

# Logging Configuration
LOG_LEVEL=INFO  # DEBUG, INFO, WARNING, ERROR, CRITICAL

# WagonHR API Configuration
WAGONHR_API_URL=https://api-wagonhr.mouritech.com
WAGONHR_USERNAME=your-username
WAGONHR_PASSWORD=your-password

# HR API URLs
LEAVE_API_BASE_URL=https://api-wagonhr.mouritech.com
ATTENDANCE_API_BASE_URL=https://api-wagonhr.mouritech.com

# Redis Configuration for Token Caching
USE_REDIS_CACHE=true
REDIS_URL=localhost:6379
REDIS_PASSWORD=

# API Timeout (in seconds)
HR_API_TIMEOUT=10
