#!/usr/bin/env python3
"""
Test script to make a real API call with actual token to test header flow.
"""

import json
import requests
import sys

def test_message_api():
    """Test the /message API with real token."""
    
    print("🚀 Testing Real API Call with Actual Token")
    print("=" * 50)
    
    # API endpoint
    url = "http://localhost:8001/message"
    
    # Request payload with real token
    payload = {
        "message": "please give me my available leave details",
        "user_id": "124",
        "session_id": "124",
        "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsImtpZCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.XPpFUVI5fYZak_OmLz1-elXavAriO5al_GvQYBR9ZGw9wzm-nyXtGzNhshC3gRYMjShVhZ3oDfi3EBkuwA0YNQFKcfGxj2K2AWq-8zCKTdQBMtjcnG2JA8RlQHwMOCE_npSq6crmqfFt8eV1XJv1yuQ2gWLV3sNIqS5kh4r6W06kElUAFt2CX_peLtqIkb7OghWDfKx58nS7iGP3wigvJeA2kBZFRt_Qj42Ce2F0ktFDTuEvvXKjWDcihA-EwRU0sVnRoWbym2oXzAxWIEjttwE-GHGdXZ1MY3EymbPttry-eiI_KtP5s1_5lWxQjywbIEFn3767hRGK2NcmD0KyAg"
    }
    
    # Headers
    headers = {
        "Content-Type": "application/json"
    }
    
    print("📋 Request Details:")
    print(f"   URL: {url}")
    print(f"   Method: POST")
    print(f"   Headers: {headers}")
    print(f"   Payload keys: {list(payload.keys())}")
    print(f"   Message: {payload['message']}")
    print(f"   User ID: {payload['user_id']}")
    print(f"   Session ID: {payload['session_id']}")
    print(f"   Token (first 50 chars): {payload['access_token'][:50]}...")
    
    try:
        print("\n🔄 Making API request...")
        response = requests.post(url, json=payload, headers=headers, timeout=30)
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"📋 Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ API call successful!")
            response_data = response.json()
            print(f"📄 Response: {json.dumps(response_data, indent=2)}")
        else:
            print(f"❌ API call failed with status {response.status_code}")
            print(f"📄 Error response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection error - is the API server running on localhost:8001?")
        print("💡 Start the server with: python api_server.py")
        return False
    except requests.exceptions.Timeout:
        print("❌ Request timeout - API took too long to respond")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        return False
    
    return True

def check_server_status():
    """Check if the API server is running."""
    
    print("🔍 Checking API server status...")
    
    try:
        response = requests.get("http://localhost:8001/", timeout=5)
        if response.status_code == 200:
            print("✅ API server is running")
            return True
        else:
            print(f"⚠️  API server responded with status {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ API server is not running")
        print("💡 Start the server with: python api_server.py")
        return False
    except Exception as e:
        print(f"❌ Error checking server: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 Real API Test with Actual Token")
    print("=" * 40)
    
    # Check if server is running
    if not check_server_status():
        print("\n🚨 Please start the API server first!")
        sys.exit(1)
    
    # Run the test
    success = test_message_api()
    
    if success:
        print("\n🎉 Test completed successfully!")
        print("💡 Check the server logs to see the complete header flow")
    else:
        print("\n💥 Test failed - check the error messages above")
