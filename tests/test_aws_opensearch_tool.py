"""
Test cases for AWS OpenSearch Policy Tool.

This module contains unit tests for the AWS OpenSearch integration tool.
"""

import pytest
import os
from unittest.mock import Mock, patch, MagicMock
from tools.aws_opensearch_policy_tool import (
    AWSOpenSearchPolicyClient,
    search_hr_policies_vector,
    search_hr_policies_hybrid,
    get_policy_by_id_opensearch,
    get_policies_by_category_opensearch
)


class TestAWSOpenSearchPolicyClient:
    """Test cases for AWSOpenSearchPolicyClient."""
    
    def setup_method(self):
        """Set up test environment."""
        # Mock environment variables
        self.env_vars = {
            "AWS_REGION": "us-east-1",
            "OPENSEARCH_ENDPOINT": "https://test-domain.us-east-1.es.amazonaws.com",
            "OPENSEARCH_POLICY_INDEX": "test-hr-policies",
            "EMBEDDING_MODEL": "all-MiniLM-L6-v2",
            "AWS_ACCESS_KEY_ID": "test-access-key",
            "AWS_SECRET_ACCESS_KEY": "test-secret-key"
        }
        
        # Apply environment variables
        for key, value in self.env_vars.items():
            os.environ[key] = value
    
    def teardown_method(self):
        """Clean up test environment."""
        # Remove test environment variables
        for key in self.env_vars.keys():
            if key in os.environ:
                del os.environ[key]
    
    @patch('tools.aws_opensearch_policy_tool.SentenceTransformer')
    @patch('tools.aws_opensearch_policy_tool.boto3.Session')
    def test_client_initialization(self, mock_session, mock_transformer):
        """Test client initialization with proper configuration."""
        # Mock SentenceTransformer
        mock_model = Mock()
        mock_transformer.return_value = mock_model
        
        # Mock boto3 session
        mock_credentials = Mock()
        mock_credentials.access_key = "test-access-key"
        mock_credentials.secret_key = "test-secret-key"
        mock_credentials.token = None
        mock_session.return_value.get_credentials.return_value = mock_credentials
        
        # Initialize client
        client = AWSOpenSearchPolicyClient()
        
        # Assertions
        assert client.region == "us-east-1"
        assert client.opensearch_endpoint == "https://test-domain.us-east-1.es.amazonaws.com"
        assert client.opensearch_index == "test-hr-policies"
        assert client.embedding_model_name == "all-MiniLM-L6-v2"
        assert not client.mock_mode
        assert client.aws_auth is not None
    
    @patch('tools.aws_opensearch_policy_tool.SentenceTransformer')
    def test_client_initialization_mock_mode(self, mock_transformer):
        """Test client initialization in mock mode."""
        # Remove OpenSearch endpoint to trigger mock mode
        if "OPENSEARCH_ENDPOINT" in os.environ:
            del os.environ["OPENSEARCH_ENDPOINT"]
        
        # Mock SentenceTransformer to fail
        mock_transformer.side_effect = Exception("Model not found")
        
        # Initialize client
        client = AWSOpenSearchPolicyClient()
        
        # Assertions
        assert client.mock_mode
        assert client.embedding_model is None
    
    @patch('tools.aws_opensearch_policy_tool.SentenceTransformer')
    def test_generate_embedding(self, mock_transformer):
        """Test embedding generation."""
        # Mock SentenceTransformer
        mock_model = Mock()
        mock_model.encode.return_value = [0.1, 0.2, 0.3]
        mock_transformer.return_value = mock_model
        
        # Initialize client
        client = AWSOpenSearchPolicyClient()
        
        # Test embedding generation
        embedding = client._generate_embedding("test query")
        
        # Assertions
        assert embedding == [0.1, 0.2, 0.3]
        mock_model.encode.assert_called_once_with("test query")
    
    def test_mock_opensearch_response(self):
        """Test mock OpenSearch response generation."""
        client = AWSOpenSearchPolicyClient()
        
        # Test search response
        search_data = {
            "query": {
                "bool": {
                    "must": [
                        {"match": {"content": "leave policy"}}
                    ]
                }
            }
        }
        
        response = client._mock_opensearch_response("POST", "/test-index/_search", search_data)
        
        # Assertions
        assert "hits" in response
        assert "total" in response["hits"]
        assert response["hits"]["total"]["value"] == 2
        assert len(response["hits"]["hits"]) == 2
        
        # Check first hit
        first_hit = response["hits"]["hits"][0]
        assert first_hit["_id"] == "POL-001"
        assert first_hit["_source"]["title"] == "Annual Leave Policy"
        assert first_hit["_source"]["category"] == "Leave"


class TestPolicySearchFunctions:
    """Test cases for policy search functions."""
    
    def setup_method(self):
        """Set up test environment."""
        # Set environment variables for mock mode
        os.environ["AWS_REGION"] = "us-east-1"
        os.environ["OPENSEARCH_POLICY_INDEX"] = "test-hr-policies"
    
    def teardown_method(self):
        """Clean up test environment."""
        # Clean up environment variables
        env_vars = ["AWS_REGION", "OPENSEARCH_POLICY_INDEX", "OPENSEARCH_ENDPOINT"]
        for var in env_vars:
            if var in os.environ:
                del os.environ[var]
    
    @patch('tools.aws_opensearch_policy_tool.opensearch_client')
    def test_search_hr_policies_vector(self, mock_client):
        """Test vector policy search function."""
        # Mock client response
        mock_response = {
            "status": "success",
            "message": "Found 2 policies matching your query",
            "results": [
                {
                    "policy_id": "POL-001",
                    "title": "Annual Leave Policy",
                    "category": "Leave",
                    "relevance_score": 0.95
                }
            ]
        }
        mock_client.search_policies_vector.return_value = mock_response
        
        # Test function
        result = search_hr_policies_vector("annual leave", top_k=5)
        
        # Assertions
        assert result["status"] == "success"
        assert len(result["results"]) == 1
        assert result["results"][0]["policy_id"] == "POL-001"
        mock_client.search_policies_vector.assert_called_once_with(
            query="annual leave",
            top_k=5,
            category_filter=None
        )
    
    @patch('tools.aws_opensearch_policy_tool.opensearch_client')
    def test_search_hr_policies_hybrid(self, mock_client):
        """Test hybrid policy search function."""
        # Mock client response
        mock_response = {
            "status": "success",
            "message": "Found 1 policy matching your query",
            "results": [
                {
                    "policy_id": "POL-002",
                    "title": "Sick Leave Policy",
                    "category": "Leave",
                    "relevance_score": 0.87
                }
            ]
        }
        mock_client.search_policies_hybrid.return_value = mock_response
        
        # Test function with category filter
        result = search_hr_policies_hybrid("sick leave", top_k=3, category_filter="Leave")
        
        # Assertions
        assert result["status"] == "success"
        assert len(result["results"]) == 1
        assert result["results"][0]["policy_id"] == "POL-002"
        mock_client.search_policies_hybrid.assert_called_once_with(
            query="sick leave",
            top_k=3,
            category_filter="Leave"
        )
    
    @patch('tools.aws_opensearch_policy_tool.opensearch_client')
    def test_get_policy_by_id_opensearch(self, mock_client):
        """Test get policy by ID function."""
        # Mock client response
        mock_response = {
            "found": True,
            "_source": {
                "policy_id": "POL-001",
                "title": "Annual Leave Policy",
                "category": "Leave",
                "content": "Full policy content here...",
                "effective_date": "2023-01-01"
            }
        }
        mock_client._make_opensearch_request.return_value = mock_response
        
        # Test function
        result = get_policy_by_id_opensearch("POL-001")
        
        # Assertions
        assert result["status"] == "success"
        assert result["policy"]["policy_id"] == "POL-001"
        assert result["policy"]["title"] == "Annual Leave Policy"
        mock_client._make_opensearch_request.assert_called_once()
    
    @patch('tools.aws_opensearch_policy_tool.opensearch_client')
    def test_get_policies_by_category_opensearch(self, mock_client):
        """Test get policies by category function."""
        # Mock client response
        mock_response = {
            "hits": {
                "hits": [
                    {
                        "_source": {
                            "policy_id": "POL-001",
                            "title": "Annual Leave Policy",
                            "category": "Leave",
                            "summary": "Policy summary",
                            "effective_date": "2023-01-01"
                        }
                    },
                    {
                        "_source": {
                            "policy_id": "POL-002",
                            "title": "Sick Leave Policy",
                            "category": "Leave",
                            "summary": "Sick leave policy summary",
                            "effective_date": "2023-01-01"
                        }
                    }
                ]
            }
        }
        mock_client._make_opensearch_request.return_value = mock_response
        
        # Test function
        result = get_policies_by_category_opensearch("Leave", top_k=10)
        
        # Assertions
        assert result["status"] == "success"
        assert result["category"] == "Leave"
        assert len(result["policies"]) == 2
        assert result["policies"][0]["policy_id"] == "POL-001"
        assert result["policies"][1]["policy_id"] == "POL-002"
        mock_client._make_opensearch_request.assert_called_once()
    
    def test_search_with_tool_context(self):
        """Test search functions with tool context for session management."""
        # Mock tool context
        mock_context = Mock()
        mock_context.state = {}
        
        # Test vector search with context
        with patch('tools.aws_opensearch_policy_tool.opensearch_client') as mock_client:
            mock_response = {
                "status": "success",
                "results": [{"policy_id": "POL-001"}]
            }
            mock_client.search_policies_vector.return_value = mock_response
            
            result = search_hr_policies_vector("test query", tool_context=mock_context)
            
            # Assertions
            assert result["status"] == "success"
            assert "last_policy_search" in mock_context.state
            assert mock_context.state["last_policy_search"]["query"] == "test query"
            assert mock_context.state["last_policy_search"]["search_type"] == "vector"


if __name__ == "__main__":
    pytest.main([__file__])
