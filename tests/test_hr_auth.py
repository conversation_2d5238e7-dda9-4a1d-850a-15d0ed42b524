"""
Test HR Authentication with Redis caching.

This script tests the HR authentication module with Redis caching.
"""

import os
import time
import logging
import unittest
from unittest.mock import patch, MagicMock

# Configure logging
logging.basicConfig(level=logging.INFO)

# Import the HR authentication module
from utils.hr_auth import HRAuthManager

class TestHRAuth(unittest.TestCase):
    """Test HR Authentication with Redis caching."""
    
    def setUp(self):
        """Set up test environment."""
        # Set environment variables for testing
        os.environ["MS_TENANT_ID"] = "test-tenant"
        os.environ["MS_CLIENT_ID"] = "test-client-id"
        os.environ["MS_CLIENT_SECRET"] = "test-client-secret"
        os.environ["WAGONHR_API_URL"] = "https://test-api.example.com"
        os.environ["WAGONHR_USERNAME"] = "test-user"
        os.environ["WAGONHR_PASSWORD"] = "test-password"
        os.environ["MOCK_HR_API"] = "true"
        
        # Create a mock Redis client
        self.mock_redis = MagicMock()
        self.mock_redis.ping.return_value = True
        self.mock_redis.hgetall.return_value = {}
        
        # Create a patch for the Redis client
        self.redis_patch = patch('redis.Redis', return_value=self.mock_redis)
        self.redis_patch.start()
    
    def tearDown(self):
        """Clean up after tests."""
        # Stop the Redis patch
        self.redis_patch.stop()
        
        # Clear environment variables
        for var in ["MS_TENANT_ID", "MS_CLIENT_ID", "MS_CLIENT_SECRET", 
                   "WAGONHR_API_URL", "WAGONHR_USERNAME", "WAGONHR_PASSWORD",
                   "MOCK_HR_API", "USE_REDIS_CACHE"]:
            if var in os.environ:
                del os.environ[var]
    
    def test_redis_caching_enabled(self):
        """Test that Redis caching works when enabled."""
        # Enable Redis caching
        os.environ["USE_REDIS_CACHE"] = "true"
        
        # Create HR auth manager
        hr_auth = HRAuthManager()
        
        # Verify Redis is enabled
        self.assertTrue(hr_auth.use_redis)
        self.assertIsNotNone(hr_auth.redis_client)
        
        # Authenticate with Microsoft
        hr_auth.authenticate_microsoft()
        
        # Verify token was saved to Redis
        self.mock_redis.hset.assert_called_with(
            hr_auth.ms_token_key, 
            mapping={
                "token": hr_auth.ms_access_token,
                "expiry": str(hr_auth.ms_token_expiry)
            }
        )
        
        # Authenticate with WagonHR
        hr_auth.authenticate_wagonhr()
        
        # Verify token was saved to Redis
        self.mock_redis.hset.assert_called_with(
            hr_auth.wagonhr_token_key, 
            mapping={
                "token": hr_auth.wagonhr_access_token,
                "expiry": str(hr_auth.wagonhr_token_expiry)
            }
        )
    
    def test_redis_caching_disabled(self):
        """Test that Redis caching is disabled when configured."""
        # Disable Redis caching
        os.environ["USE_REDIS_CACHE"] = "false"
        
        # Create HR auth manager
        hr_auth = HRAuthManager()
        
        # Verify Redis is disabled
        self.assertFalse(hr_auth.use_redis)
        
        # Authenticate with Microsoft
        hr_auth.authenticate_microsoft()
        
        # Verify token was not saved to Redis
        self.mock_redis.hset.assert_not_called()
    
    def test_token_loading_from_redis(self):
        """Test that tokens are loaded from Redis when available."""
        # Enable Redis caching
        os.environ["USE_REDIS_CACHE"] = "true"
        
        # Set up mock Redis data
        ms_token = "mock-ms-token-from-redis"
        ms_expiry = time.time() + 3600
        wagonhr_token = "mock-wagonhr-token-from-redis"
        wagonhr_expiry = time.time() + 3600
        
        # Configure mock Redis to return tokens
        def mock_hgetall(key):
            if key == "hr_auth:ms_token":
                return {"token": ms_token, "expiry": str(ms_expiry)}
            elif key == "hr_auth:wagonhr_token":
                return {"token": wagonhr_token, "expiry": str(wagonhr_expiry)}
            return {}
        
        self.mock_redis.hgetall.side_effect = mock_hgetall
        
        # Create HR auth manager
        hr_auth = HRAuthManager()
        
        # Verify tokens were loaded from Redis
        self.assertEqual(hr_auth.ms_access_token, ms_token)
        self.assertEqual(hr_auth.ms_token_expiry, ms_expiry)
        self.assertEqual(hr_auth.wagonhr_access_token, wagonhr_token)
        self.assertEqual(hr_auth.wagonhr_token_expiry, wagonhr_expiry)
        
        # Get auth headers
        ms_headers = hr_auth.get_ms_auth_headers()
        wagonhr_headers = hr_auth.get_wagonhr_auth_headers()
        
        # Verify headers contain the tokens from Redis
        self.assertEqual(ms_headers["Authorization"], f"Bearer {ms_token}")
        self.assertEqual(wagonhr_headers["Authorization"], f"Bearer {wagonhr_token}")
    
    def test_token_refresh_when_expired(self):
        """Test that tokens are refreshed when expired."""
        # Enable Redis caching
        os.environ["USE_REDIS_CACHE"] = "true"
        
        # Set up mock Redis data with expired tokens
        ms_token = "expired-ms-token"
        ms_expiry = time.time() - 3600  # Expired 1 hour ago
        wagonhr_token = "expired-wagonhr-token"
        wagonhr_expiry = time.time() - 3600  # Expired 1 hour ago
        
        # Configure mock Redis to return expired tokens
        def mock_hgetall(key):
            if key == "hr_auth:ms_token":
                return {"token": ms_token, "expiry": str(ms_expiry)}
            elif key == "hr_auth:wagonhr_token":
                return {"token": wagonhr_token, "expiry": str(wagonhr_expiry)}
            return {}
        
        self.mock_redis.hgetall.side_effect = mock_hgetall
        
        # Create HR auth manager
        hr_auth = HRAuthManager()
        
        # Verify expired tokens were loaded from Redis
        self.assertEqual(hr_auth.ms_access_token, ms_token)
        self.assertEqual(hr_auth.ms_token_expiry, ms_expiry)
        
        # Get auth headers (should trigger token refresh)
        ms_headers = hr_auth.get_ms_auth_headers()
        
        # Verify new token was generated
        self.assertNotEqual(hr_auth.ms_access_token, ms_token)
        self.assertGreater(hr_auth.ms_token_expiry, time.time())
        
        # Verify new token was saved to Redis
        self.mock_redis.hset.assert_called_with(
            hr_auth.ms_token_key, 
            mapping={
                "token": hr_auth.ms_access_token,
                "expiry": str(hr_auth.ms_token_expiry)
            }
        )

if __name__ == "__main__":
    unittest.main()
