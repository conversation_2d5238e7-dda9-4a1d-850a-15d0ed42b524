"""
Test script for HR Agent functionality.

This script tests the HR Agent functionality with mock data.
"""

import os
import sys
import datetime
import json
import logging
from typing import Dict, Any

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Mock the google.adk.tools.tool_context module
class MockToolContext:
    def __init__(self):
        self.state = {}

sys.modules['google'] = type('MockGoogle', (), {})
sys.modules['google.adk'] = type('MockAdk', (), {})
sys.modules['google.adk.tools'] = type('MockTools', (), {})
sys.modules['google.adk.tools.tool_context'] = type('MockToolContext', (), {'ToolContext': MockToolContext})

# Import HR tools
try:
    from tools.hr_api import get_employee, get_leave_balance, request_leave, record_attendance
    from tools.leave_tools import approve_leave, reject_leave, get_leave_history, modify_leave, cancel_leave
    from tools.attendance_tools import verify_attendance, get_attendance_report, adjust_timezone_attendance, get_attendance_anomalies
    from tools.geo_tools import verify_location, get_office_locations, update_employee_location, get_location_history
    #from tools.project_tools import get_project_schedule, suggest_leave_dates, check_workload
except ImportError as e:
    logger.error(f"Error importing modules: {str(e)}")
    print(f"Error importing modules: {str(e)}")

    # Define mock implementations for testing
    def get_employee(employee_id=None, email=None, include_leave_balance=False, include_manager=False, tool_context=None):
        return {
            "status": "success",
            "message": "Employee information retrieved successfully",
            "employee": {
                "id": "EMP001",
                "email": "<EMAIL>",
                "full_name": "Alice Smith",
                "department": "Engineering",
                "position": "Senior Developer",
                "leave_balance": {
                    "casual": 10,
                    "sick": 15,
                    "annual": 20
                }
            }
        }

    def get_leave_balance(employee_id=None, email=None, tool_context=None):
        return {
            "status": "success",
            "message": "Leave balance retrieved successfully",
            "leave_balance": {
                "employee_id": "EMP001",
                "employee_email": "<EMAIL>",
                "employee_name": "Alice Smith",
                "leave_balance": {
                    "casual": 10,
                    "sick": 15,
                    "annual": 20
                }
            }
        }

    def request_leave(employee_id=None, email=None, start_date=None, end_date=None, leave_type=None, reason=None, half_day=False, tool_context=None):
        return {
            "status": "success",
            "message": "Leave request created successfully",
            "leave_request": {
                "id": "LR-1001",
                "employee_email": email or "<EMAIL>",
                "start_date": start_date,
                "end_date": end_date,
                "leave_type": leave_type,
                "reason": reason,
                "status": "Pending"
            }
        }

    def record_attendance(employee_id=None, email=None, status=None, timestamp=None, location=None, notes=None, tool_context=None):
        return {
            "status": "success",
            "message": "Attendance recorded successfully",
            "attendance_id": "ATT-1001",
            "attendance_record": {
                "id": "ATT-1001",
                "employee_email": email or "<EMAIL>",
                "status": status,
                "timestamp": timestamp or datetime.datetime.now().isoformat(),
                "location": location
            }
        }

    # Define other mock functions
    approve_leave = lambda **kwargs: {"status": "success", "message": "Leave approved successfully"}
    reject_leave = lambda **kwargs: {"status": "success", "message": "Leave rejected successfully"}
    get_leave_history = lambda **kwargs: {"status": "success", "message": "Leave history retrieved successfully", "records": []}
    modify_leave = lambda **kwargs: {"status": "success", "message": "Leave modified successfully"}
    cancel_leave = lambda **kwargs: {"status": "success", "message": "Leave cancelled successfully"}

    verify_attendance = lambda **kwargs: {"status": "success", "message": "Attendance verified successfully"}
    get_attendance_report = lambda **kwargs: {"status": "success", "message": "Attendance report retrieved successfully", "report": {}}
    adjust_timezone_attendance = lambda **kwargs: {"status": "success", "message": "Attendance timezone adjusted successfully"}
    get_attendance_anomalies = lambda **kwargs: {"status": "success", "message": "Attendance anomalies detected", "anomalies": []}

    verify_location = lambda **kwargs: {"status": "success", "message": "Location verified successfully", "within_geofence": True}
    get_office_locations = lambda **kwargs: {"status": "success", "message": "Office locations retrieved successfully", "offices": []}
    update_employee_location = lambda **kwargs: {"status": "success", "message": "Employee location updated successfully"}
    get_location_history = lambda **kwargs: {"status": "success", "message": "Location history retrieved successfully", "location_history": []}

    #get_project_schedule = lambda **kwargs: {"status": "success", "message": "Project schedule retrieved successfully", "schedule": []}
    suggest_leave_dates = lambda **kwargs: {"status": "success", "message": "Leave dates suggested successfully", "suggestions": []}
    check_workload = lambda **kwargs: {"status": "success", "message": "Workload checked successfully", "workload": "medium"}


def print_result(result: Dict[str, Any]) -> None:
    """Prints the result of a function call in a formatted way.

    Args:
        result: The result dictionary to print
    """
    print("\nResult:")
    print(f"Status: {result.get('status')}")
    print(f"Message: {result.get('message')}")

    # Print other keys except for large nested objects
    for key, value in result.items():
        if key not in ['status', 'message']:
            if isinstance(value, dict) and len(str(value)) > 100:
                print(f"{key}: <complex object>")
            elif isinstance(value, list) and len(str(value)) > 100:
                print(f"{key}: <list with {len(value)} items>")
            else:
                print(f"{key}: {value}")


def test_leave_management():
    """Tests leave management functionality."""
    print("\n=== Testing Leave Management ===")

    # Test get employee
    print("\nGetting employee information...")
    employee_result = get_employee(email="<EMAIL>", include_leave_balance=True, include_manager=True)
    print_result(employee_result)

    # Test get leave balance
    print("\nGetting leave balance...")
    balance_result = get_leave_balance(email="<EMAIL>")
    print_result(balance_result)

    # Test request leave
    print("\nRequesting leave...")
    today = datetime.date.today()
    start_date = (today + datetime.timedelta(days=7)).isoformat()
    end_date = (today + datetime.timedelta(days=9)).isoformat()

    request_result = request_leave(
        email="<EMAIL>",
        start_date=start_date,
        end_date=end_date,
        leave_type="annual",
        reason="Vacation",
        half_day=False
    )
    print_result(request_result)

    if request_result["status"] == "success":
        leave_id = request_result["leave_request"]["id"]

        # Test approve leave
        print("\nApproving leave...")
        approve_result = approve_leave(
            leave_id=leave_id,
            approver_email="<EMAIL>",
            notes="Approved. Enjoy your vacation!"
        )
        print_result(approve_result)

        # Test get leave history
        print("\nGetting leave history...")
        history_result = get_leave_history(
            employee_email="<EMAIL>"
        )
        print_result(history_result)

    # Test suggest leave dates
    print("\nSuggesting leave dates...")
    suggest_result = suggest_leave_dates(
        employee_email="<EMAIL>",
        duration_days=5
    )
    print_result(suggest_result)


def test_attendance_tracking():
    """Tests attendance tracking functionality."""
    print("\n=== Testing Attendance Tracking ===")

    # Test record attendance
    print("\nRecording attendance...")
    attendance_result = record_attendance(
        email="<EMAIL>",
        status="present",
        location={"lat": 40.7128, "lng": -74.0060}
    )
    print_result(attendance_result)

    if attendance_result["status"] == "success":
        attendance_id = attendance_result["attendance_id"]

        # Test verify attendance
        print("\nVerifying attendance...")
        verify_result = verify_attendance(
            attendance_id=attendance_id,
            verifier_email="<EMAIL>",
            verification_status="verified",
            notes="Verified via system"
        )
        print_result(verify_result)

    # Test get attendance report
    print("\nGetting attendance report...")
    today = datetime.date.today()
    start_date = (today - datetime.timedelta(days=30)).isoformat()
    end_date = today.isoformat()

    report_result = get_attendance_report(
        employee_email="<EMAIL>",
        start_date=start_date,
        end_date=end_date
    )
    print_result(report_result)

    # Test timezone adjustment
    print("\nAdjusting timezone...")
    timezone_result = adjust_timezone_attendance(
        employee_email="<EMAIL>",
        source_timezone="America/New_York",
        target_timezone="Asia/Kolkata"
    )
    print_result(timezone_result)


def test_geo_fencing():
    """Tests geo-fencing functionality."""
    print("\n=== Testing Geo-Fencing ===")

    # Test verify location
    print("\nVerifying location...")
    location_result = verify_location(
        employee_email="<EMAIL>",
        latitude=40.7128,
        longitude=-74.0060
    )
    print_result(location_result)

    # Test get office locations
    print("\nGetting office locations...")
    offices_result = get_office_locations()
    print_result(offices_result)

    # Test update employee location
    print("\nUpdating employee location...")
    update_result = update_employee_location(
        employee_email="<EMAIL>",
        office_name="San Francisco Office",
        remote_work=True
    )
    print_result(update_result)

    # Test get location history
    print("\nGetting location history...")
    history_result = get_location_history(
        employee_email="<EMAIL>"
    )
    print_result(history_result)


def test_workload_analysis():
    """Tests workload analysis functionality."""
    print("\n=== Testing Workload Analysis ===")

    # Test get project schedule
    print("\nGetting project schedule...")
    today = datetime.date.today()
    start_date = today.isoformat()
    end_date = (today + datetime.timedelta(days=30)).isoformat()

    schedule_result = get_project_schedule(
        employee_id="EMP001",
        start_date=start_date,
        end_date=end_date
    )
    print_result(schedule_result)

    # Test check workload
    print("\nChecking workload...")
    workload_result = check_workload(
        employee_email="<EMAIL>",
        start_date=start_date,
        end_date=end_date
    )
    print_result(workload_result)

    # Test suggest leave dates
    print("\nSuggesting leave dates based on workload...")
    suggest_result = suggest_leave_dates(
        employee_email="<EMAIL>",
        duration_days=3
    )
    print_result(suggest_result)


def test_anomaly_detection():
    """Tests anomaly detection functionality."""
    print("\n=== Testing Anomaly Detection ===")

    # Create some attendance records with anomalies
    print("\nCreating attendance records with anomalies...")

    # Record late attendance
    for i in range(3):
        day = datetime.date.today() - datetime.timedelta(days=i*2)
        timestamp = datetime.datetime.combine(day, datetime.time(10, 30)).isoformat()

        record_attendance(
            email="<EMAIL>",
            status="late",
            timestamp=timestamp,
            notes=f"Late arrival on {day.isoformat()}"
        )

    # Record attendance from different locations
    locations = [
        {"lat": 40.7128, "lng": -74.0060},  # New York
        {"lat": 37.7749, "lng": -122.4194},  # San Francisco
        {"lat": 51.5074, "lng": -0.1278}     # London
    ]

    for i, location in enumerate(locations):
        day = datetime.date.today() - datetime.timedelta(days=i)
        timestamp = datetime.datetime.combine(day, datetime.time(9, 0)).isoformat()

        record_attendance(
            email="<EMAIL>",
            status="present",
            timestamp=timestamp,
            location=location
        )

    # Test get attendance anomalies
    print("\nGetting attendance anomalies...")
    today = datetime.date.today()
    start_date = (today - datetime.timedelta(days=30)).isoformat()
    end_date = today.isoformat()

    anomalies_result = get_attendance_anomalies(
        employee_email="<EMAIL>",
        start_date=start_date,
        end_date=end_date,
        anomaly_types=["late", "location"]
    )
    print_result(anomalies_result)


def main():
    """Main function to run all tests."""
    print("Starting HR Agent tests...")

    try:
        # Test leave management
        test_leave_management()

        # Test attendance tracking
        test_attendance_tracking()

        # Test geo-fencing
        test_geo_fencing()

        # Test workload analysis
        test_workload_analysis()

        # Test anomaly detection
        test_anomaly_detection()

        print("\nAll tests completed successfully!")
    except Exception as e:
        logger.error(f"Error during testing: {str(e)}", exc_info=True)
        print(f"\nError during testing: {str(e)}")


if __name__ == "__main__":
    main()
