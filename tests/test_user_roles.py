"""
Test script for user roles and permissions.

This script tests the role-based access control for password resets.
"""

import logging
import sys
import os

# Add the parent directory to the path so we can import the modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tools.user_roles import is_admin_user, can_reset_password

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_is_admin_user():
    """Test the is_admin_user function."""
    logger.info("=== Testing is_admin_user function ===")
    
    # Test with admin user
    admin_email = "<EMAIL>"
    result = is_admin_user(admin_email)
    logger.info(f"is_admin_user({admin_email}) = {result}")
    assert result == True, f"User {admin_email} should be recognized as an admin"
    
    # Test with non-admin user
    regular_email = "<EMAIL>"
    result = is_admin_user(regular_email)
    logger.info(f"is_admin_user({regular_email}) = {result}")
    assert result == False, f"User {regular_email} should not be recognized as an admin"
    
    # Test with force_admin=True
    result = is_admin_user(regular_email, force_admin=True)
    logger.info(f"is_admin_user({regular_email}, force_admin=True) = {result}")
    assert result == True, "force_admin=True should override and return True"
    
    # Test with force_regular=True
    result = is_admin_user(admin_email, force_regular=True)
    logger.info(f"is_admin_user({admin_email}, force_regular=True) = {result}")
    assert result == False, "force_regular=True should override and return False"
    
    logger.info("All is_admin_user tests passed!\n")

def test_can_reset_password():
    """Test the can_reset_password function."""
    logger.info("=== Testing can_reset_password function ===")
    
    # Test user resetting their own password
    user_email = "<EMAIL>"
    result = can_reset_password(user_email, user_email)
    logger.info(f"can_reset_password({user_email}, {user_email}) = {result}")
    assert result == True, "User should be able to reset their own password"
    
    # Test non-admin user trying to reset another user's password
    other_user_email = "<EMAIL>"
    result = can_reset_password(user_email, other_user_email)
    logger.info(f"can_reset_password({user_email}, {other_user_email}) = {result}")
    assert result == False, "Non-admin user should not be able to reset another user's password"
    
    # Test admin user trying to reset another user's password
    admin_email = "<EMAIL>"
    result = can_reset_password(admin_email, user_email)
    logger.info(f"can_reset_password({admin_email}, {user_email}) = {result}")
    assert result == True, "Admin user should be able to reset another user's password"
    
    # Test with default mock values
    result = can_reset_password()
    logger.info(f"can_reset_password() = {result}")
    assert result == False, "Default mock values should represent a non-admin user trying to reset another user's password"
    
    logger.info("All can_reset_password tests passed!\n")

if __name__ == "__main__":
    logger.info("Starting user roles and permissions tests")
    
    try:
        test_is_admin_user()
        test_can_reset_password()
        
        logger.info("All tests passed!")
    except AssertionError as e:
        logger.error(f"Test failed: {e}")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
