"""
Test authorization checks for password reset.

This module tests that authorization checks are performed correctly
when a user requests a password reset.
"""

import logging
import unittest
from unittest.mock import patch, MagicMock

from tools.freshservice import create_password_reset_incident
from tools.user_roles import can_reset_password, is_admin_user, check_password_reset_authorization

# Set up logger
logger = logging.getLogger(__name__)

class TestAuthorizationCheck(unittest.TestCase):
    """Test cases for authorization checks in password reset workflow."""

    @patch('tools.user_roles.is_admin_user')
    @patch('tools.user_roles.can_reset_password')
    @patch('tools.freshservice.create_incident')
    def test_same_user_reset_authorized(self, mock_create_incident, mock_can_reset, mock_is_admin):
        """Test that a user can reset their own password."""
        # Setup mocks
        mock_is_admin.return_value = False
        mock_can_reset.return_value = True
        mock_create_incident.return_value = {"status": "success", "ticket_id": "12345"}

        # Test with same user
        user_email = "<EMAIL>"
        result = create_password_reset_incident(
            requester_email=user_email,
            target_email=user_email
        )

        # Verify results
        self.assertEqual(result["status"], "success")
        self.assertTrue(result["authorized"])
        self.assertFalse(result["is_admin"])
        mock_can_reset.assert_called_once_with(user_email, user_email)
        mock_create_incident.assert_called_once()

    @patch('tools.user_roles.is_admin_user')
    @patch('tools.user_roles.can_reset_password')
    @patch('tools.freshservice.create_incident')
    def test_non_admin_reset_other_unauthorized(self, mock_create_incident, mock_can_reset, mock_is_admin):
        """Test that a non-admin user cannot reset another user's password."""
        # Setup mocks
        mock_is_admin.return_value = False
        mock_can_reset.return_value = False

        # Test with non-admin user trying to reset another user's password
        user_email = "<EMAIL>"
        other_user_email = "<EMAIL>"
        result = create_password_reset_incident(
            requester_email=user_email,
            target_email=other_user_email
        )

        # Verify results
        self.assertEqual(result["status"], "error")
        self.assertFalse(result["authorized"])
        self.assertFalse(result["is_admin"])
        mock_can_reset.assert_called_once_with(user_email, other_user_email)
        mock_create_incident.assert_not_called()

    @patch('tools.user_roles.is_admin_user')
    @patch('tools.user_roles.can_reset_password')
    @patch('tools.freshservice.create_incident')
    def test_admin_reset_other_authorized(self, mock_create_incident, mock_can_reset, mock_is_admin):
        """Test that an admin user can reset another user's password."""
        # Setup mocks
        mock_is_admin.return_value = True
        mock_can_reset.return_value = True
        mock_create_incident.return_value = {"status": "success", "ticket_id": "12345"}

        # Test with admin user trying to reset another user's password
        admin_email = "<EMAIL>"
        user_email = "<EMAIL>"
        result = create_password_reset_incident(
            requester_email=admin_email,
            target_email=user_email
        )

        # Verify results
        self.assertEqual(result["status"], "success")
        self.assertTrue(result["authorized"])
        self.assertTrue(result["is_admin"])
        mock_can_reset.assert_called_once_with(admin_email, user_email)
        mock_create_incident.assert_called_once()

    @patch('tools.user_roles.is_admin_user')
    @patch('tools.user_roles.can_reset_password')
    def test_error_message_contains_target_email(self, mock_can_reset, mock_is_admin):
        """Test that the error message contains the target email."""
        # Setup mocks
        mock_is_admin.return_value = False
        mock_can_reset.return_value = False

        # Test with non-admin user trying to reset another user's password
        user_email = "<EMAIL>"
        target_email = "<EMAIL>"
        result = create_password_reset_incident(
            requester_email=user_email,
            target_email=target_email
        )

        # Verify results
        self.assertEqual(result["status"], "error")
        self.assertFalse(result["authorized"])
        self.assertIn(target_email, result["message"])
        self.assertEqual(result["target_email"], target_email)

    @patch('tools.user_roles.is_admin_user')
    @patch('tools.user_roles.can_reset_password')
    @patch('tools.freshservice.create_incident')
    def test_authorization_with_no_requester_email(self, mock_create_incident, mock_can_reset, mock_is_admin):
        """Test authorization check when no requester email is provided."""
        # Setup mocks
        mock_is_admin.return_value = False
        mock_can_reset.return_value = True  # Mock that the user can reset their own password
        mock_create_incident.return_value = {"status": "success", "ticket_id": "12345"}

        # Test with only target email
        target_email = "<EMAIL>"
        result = create_password_reset_incident(
            requester_email=None,
            target_email=target_email
        )

        # Verify results
        self.assertEqual(result["status"], "success")
        self.assertTrue(result["authorized"])
        self.assertFalse(result["is_admin"])
        # Verify can_reset_password was called with None for requester_email
        # It should use the mock value internally
        mock_can_reset.assert_called_once_with(None, target_email)
        mock_create_incident.assert_called_once()

    @patch('tools.user_roles.is_admin_user')
    @patch('tools.user_roles.can_reset_password')
    @patch('tools.freshservice.create_incident')
    def test_unauthorized_with_no_requester_email(self, mock_create_incident, mock_can_reset, mock_is_admin):
        """Test authorization denied when no requester email is provided."""
        # Setup mocks
        mock_is_admin.return_value = False
        mock_can_reset.return_value = False  # Mock that the user cannot reset the password

        # Test with only target email
        target_email = "<EMAIL>"  # Try to reset admin password
        result = create_password_reset_incident(
            requester_email=None,
            target_email=target_email
        )

        # Verify results
        self.assertEqual(result["status"], "error")
        self.assertFalse(result["authorized"])
        self.assertFalse(result["is_admin"])
        # Verify can_reset_password was called with None for requester_email
        mock_can_reset.assert_called_once_with(None, target_email)
        mock_create_incident.assert_not_called()

    @patch('tools.user_roles.is_admin_user')
    @patch('tools.user_roles.can_reset_password')
    def test_check_authorization_same_user(self, mock_can_reset, mock_is_admin):
        """Test check_password_reset_authorization for same user."""
        # Setup mocks
        mock_is_admin.return_value = False
        mock_can_reset.return_value = True

        # Test with same user
        user_email = "<EMAIL>"
        result = check_password_reset_authorization(
            target_user_email=user_email,
            requesting_user_email=user_email
        )

        # Verify results
        self.assertEqual(result["status"], "success")
        self.assertTrue(result["authorized"])
        self.assertFalse(result["is_admin"])
        mock_can_reset.assert_called_once_with(user_email, user_email)

    @patch('tools.user_roles.is_admin_user')
    @patch('tools.user_roles.can_reset_password')
    def test_check_authorization_non_admin_reset_other(self, mock_can_reset, mock_is_admin):
        """Test check_password_reset_authorization for non-admin resetting other user."""
        # Setup mocks
        mock_is_admin.return_value = False
        mock_can_reset.return_value = False

        # Test with non-admin user trying to reset another user's password
        user_email = "<EMAIL>"
        other_user_email = "<EMAIL>"
        result = check_password_reset_authorization(
            target_user_email=other_user_email,
            requesting_user_email=user_email
        )

        # Verify results
        self.assertEqual(result["status"], "error")
        self.assertFalse(result["authorized"])
        self.assertFalse(result["is_admin"])
        mock_can_reset.assert_called_once_with(user_email, other_user_email)

    @patch('tools.user_roles.is_admin_user')
    @patch('tools.user_roles.can_reset_password')
    def test_check_authorization_admin_reset_other(self, mock_can_reset, mock_is_admin):
        """Test check_password_reset_authorization for admin resetting other user."""
        # Setup mocks
        mock_is_admin.return_value = True
        mock_can_reset.return_value = True

        # Test with admin user trying to reset another user's password
        admin_email = "<EMAIL>"
        user_email = "<EMAIL>"
        result = check_password_reset_authorization(
            target_user_email=user_email,
            requesting_user_email=admin_email
        )

        # Verify results
        self.assertEqual(result["status"], "success")
        self.assertTrue(result["authorized"])
        self.assertTrue(result["is_admin"])
        mock_can_reset.assert_called_once_with(admin_email, user_email)

    @patch('tools.user_roles.is_admin_user')
    @patch('tools.user_roles.can_reset_password')
    def test_check_authorization_with_no_requester_email(self, mock_can_reset, mock_is_admin):
        """Test check_password_reset_authorization with no requester email."""
        # Setup mocks
        mock_is_admin.return_value = False
        mock_can_reset.return_value = True  # Mock that the user can reset their own password

        # Test with only target email
        target_email = "<EMAIL>"
        result = check_password_reset_authorization(
            target_user_email=target_email,
            requesting_user_email=None
        )

        # Verify results
        self.assertEqual(result["status"], "success")
        self.assertTrue(result["authorized"])
        self.assertFalse(result["is_admin"])
        # Verify can_reset_password was called with None for requester_email
        # It should use the mock value internally
        mock_can_reset.assert_called_once_with(None, target_email)

if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(level=logging.INFO)
    unittest.main()
