"""
Test script for password reset permissions.

This script tests the role-based access control for password resets.
"""

import logging
import sys
import os

# Add the parent directory to the path so we can import the modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tools.user_roles import is_admin_user, can_reset_password

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_same_user_reset():
    """Test that a user can reset their own password."""
    user_email = "<EMAIL>"

    # Test with same user
    logger.info("=== Testing user resetting their own password ===")
    result = reset_password(
        user_identifier=user_email,
        requesting_user_email=user_email,
        is_admin=False
    )

    logger.info(f"Result: {result['status']}")
    assert result['status'] == "success", "User should be able to reset their own password"
    logger.info("Test passed: User can reset their own password\n")

def test_non_admin_reset_other():
    """Test that a non-admin user cannot reset another user's password."""
    user_email = "<EMAIL>"
    other_user_email = "<EMAIL>"

    # Test with non-admin user trying to reset another user's password
    logger.info("=== Testing non-admin user resetting another user's password ===")
    result = reset_password(
        user_identifier=other_user_email,
        requesting_user_email=user_email,
        is_admin=False
    )

    logger.info(f"Result: {result['status']}")
    assert result['status'] == "error", "Non-admin user should not be able to reset another user's password"
    logger.info("Test passed: Non-admin user cannot reset another user's password\n")

def test_admin_reset_other():
    """Test that an admin user can reset another user's password."""
    admin_email = "<EMAIL>"
    user_email = "<EMAIL>"

    # Test with admin user trying to reset another user's password
    logger.info("=== Testing admin user resetting another user's password ===")
    result = reset_password(
        user_identifier=user_email,
        requesting_user_email=admin_email,
        is_admin=True
    )

    logger.info(f"Result: {result['status']}")
    assert result['status'] == "success", "Admin user should be able to reset another user's password"
    logger.info("Test passed: Admin user can reset another user's password\n")

def test_same_user_unlock():
    """Test that a user can unlock their own account."""
    user_email = "<EMAIL>"

    # Test with same user
    logger.info("=== Testing user unlocking their own account ===")
    result = unlock_account(
        user_identifier=user_email,
        requesting_user_email=user_email,
        is_admin=False
    )

    logger.info(f"Result: {result['status']}")
    assert result['status'] == "success", "User should be able to unlock their own account"
    logger.info("Test passed: User can unlock their own account\n")

def test_non_admin_unlock_other():
    """Test that a non-admin user cannot unlock another user's account."""
    user_email = "<EMAIL>"
    other_user_email = "<EMAIL>"

    # Test with non-admin user trying to unlock another user's account
    logger.info("=== Testing non-admin user unlocking another user's account ===")
    result = unlock_account(
        user_identifier=other_user_email,
        requesting_user_email=user_email,
        is_admin=False
    )

    logger.info(f"Result: {result['status']}")
    assert result['status'] == "error", "Non-admin user should not be able to unlock another user's account"
    logger.info("Test passed: Non-admin user cannot unlock another user's account\n")

def test_admin_unlock_other():
    """Test that an admin user can unlock another user's account."""
    admin_email = "<EMAIL>"
    user_email = "<EMAIL>"

    # Test with admin user trying to unlock another user's account
    logger.info("=== Testing admin user unlocking another user's account ===")
    result = unlock_account(
        user_identifier=user_email,
        requesting_user_email=admin_email,
        is_admin=True
    )

    logger.info(f"Result: {result['status']}")
    assert result['status'] == "success", "Admin user should be able to unlock another user's account"
    logger.info("Test passed: Admin user can unlock another user's account\n")

if __name__ == "__main__":
    logger.info("Starting password reset permissions tests")

    try:
        test_same_user_reset()
        test_non_admin_reset_other()
        test_admin_reset_other()
        test_same_user_unlock()
        test_non_admin_unlock_other()
        test_admin_unlock_other()

        logger.info("All tests passed!")
    except AssertionError as e:
        logger.error(f"Test failed: {e}")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
