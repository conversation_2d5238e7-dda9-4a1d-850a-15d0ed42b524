"""
Test cases for Dynamic WagonHR API Agent.

This module contains test cases for the dynamic WagonHR agent that can
automatically call APIs based on Swagger specifications.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from tools.dynamic_wagonhr_api_agent import (
    DynamicWagonHRAgent,
    process_wagonhr_request,
    smart_wagonhr_assistant,
    get_available_operations
)


class TestDynamicWagonHRAgent:
    """Test cases for DynamicWagonHRAgent."""
    
    def setup_method(self):
        """Set up test environment."""
        self.agent = DynamicWagonHRAgent()
    
    def test_find_relevant_endpoint_leave_balance(self):
        """Test finding endpoint for leave balance request."""
        user_request = "How many leave days do I have left?"
        endpoint = self.agent.find_relevant_endpoint(user_request)
        
        assert endpoint is not None
        assert "balance" in endpoint["summary"].lower() or "balance" in endpoint["path"].lower()
    
    def test_find_relevant_endpoint_leave_request(self):
        """Test finding endpoint for leave request submission."""
        user_request = "I want to apply for leave from 2024-01-15 to 2024-01-17"
        endpoint = self.agent.find_relevant_endpoint(user_request)
        
        assert endpoint is not None
        assert "request" in endpoint["summary"].lower() or "submit" in endpoint["summary"].lower()
    
    def test_find_relevant_endpoint_attendance(self):
        """Test finding endpoint for attendance recording."""
        user_request = "I want to record my attendance for today"
        endpoint = self.agent.find_relevant_endpoint(user_request)
        
        assert endpoint is not None
        assert "attendance" in endpoint["summary"].lower() or "record" in endpoint["summary"].lower()
    
    def test_extract_parameters_leave_request(self):
        """Test parameter extraction for leave request."""
        user_request = "I want to apply for casual leave from 2024-01-15 to 2024-01-17 because I'm sick"
        endpoint = {"summary": "Submit leave request", "method": "POST"}
        
        parameters = self.agent.extract_parameters_from_request(user_request, endpoint)
        
        assert parameters["startDate"] == "2024-01-15"
        assert parameters["endDate"] == "2024-01-17"
        assert parameters["leaveType"] == "casual"
        assert "sick" in parameters["reason"]
    
    def test_extract_parameters_attendance(self):
        """Test parameter extraction for attendance recording."""
        user_request = "I want to check in as present today"
        endpoint = {"summary": "Record attendance", "method": "POST"}
        
        parameters = self.agent.extract_parameters_from_request(user_request, endpoint)
        
        assert parameters["status"] == "present"
    
    @patch('tools.dynamic_wagonhr_api_agent.requests.get')
    @patch('tools.dynamic_wagonhr_api_agent.hr_auth_manager')
    def test_make_api_call_success(self, mock_auth, mock_get):
        """Test successful API call."""
        # Mock authentication
        mock_auth.get_wagonhr_token.return_value = "test-token"
        
        # Mock successful response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"status": "success", "data": {"balance": 10}}
        mock_get.return_value = mock_response
        
        endpoint = {
            "path": "/api/hrms/leave/balance",
            "method": "GET",
            "summary": "Get leave balance"
        }
        parameters = {}
        
        result = self.agent.make_api_call(endpoint, parameters)
        
        assert result["status"] == "success"
        assert "data" in result
        mock_get.assert_called_once()
    
    @patch('tools.dynamic_wagonhr_api_agent.hr_auth_manager')
    def test_make_api_call_no_token(self, mock_auth):
        """Test API call without authentication token."""
        # Mock no token available
        mock_auth.get_wagonhr_token.return_value = None
        
        endpoint = {
            "path": "/api/hrms/leave/balance",
            "method": "GET",
            "summary": "Get leave balance"
        }
        parameters = {}
        
        result = self.agent.make_api_call(endpoint, parameters)
        
        assert result["status"] == "error"
        assert "Authentication token not available" in result["message"]


class TestProcessWagonHRRequest:
    """Test cases for process_wagonhr_request function."""
    
    @patch('tools.dynamic_wagonhr_api_agent.dynamic_wagonhr_agent')
    def test_process_leave_balance_request(self, mock_agent):
        """Test processing leave balance request."""
        # Mock endpoint finding
        mock_endpoint = {
            "path": "/api/hrms/leave/balance",
            "method": "GET",
            "summary": "Get leave balance"
        }
        mock_agent.find_relevant_endpoint.return_value = mock_endpoint
        
        # Mock parameter extraction
        mock_agent.extract_parameters_from_request.return_value = {}
        
        # Mock API call
        mock_agent.make_api_call.return_value = {
            "status": "success",
            "data": {"leave_balance": {"casual": 5, "sick": 3}}
        }
        
        user_request = "How many leave days do I have?"
        result = process_wagonhr_request(user_request)
        
        assert result["status"] == "success"
        assert "data" in result
        mock_agent.find_relevant_endpoint.assert_called_once_with(user_request)
        mock_agent.make_api_call.assert_called_once()
    
    @patch('tools.dynamic_wagonhr_api_agent.dynamic_wagonhr_agent')
    def test_process_request_no_endpoint_found(self, mock_agent):
        """Test processing request when no endpoint is found."""
        # Mock no endpoint found
        mock_agent.find_relevant_endpoint.return_value = None
        
        user_request = "Some random request"
        result = process_wagonhr_request(user_request)
        
        assert result["status"] == "error"
        assert "Could not find a relevant API endpoint" in result["message"]
        assert "available_operations" in result
    
    def test_process_request_with_context(self):
        """Test processing request with tool context."""
        mock_context = Mock()
        mock_context.state = {}
        
        with patch('tools.dynamic_wagonhr_api_agent.dynamic_wagonhr_agent') as mock_agent:
            # Mock successful processing
            mock_endpoint = {"summary": "Test endpoint"}
            mock_agent.find_relevant_endpoint.return_value = mock_endpoint
            mock_agent.extract_parameters_from_request.return_value = {"test": "param"}
            mock_agent.make_api_call.return_value = {"status": "success", "data": {}}
            
            user_request = "Test request"
            result = process_wagonhr_request(user_request, mock_context)
            
            assert result["status"] == "success"
            assert "last_wagonhr_request" in mock_context.state


class TestSmartWagonHRAssistant:
    """Test cases for smart_wagonhr_assistant function."""
    
    def test_help_request(self):
        """Test handling help requests."""
        user_request = "What can you help me with?"
        result = smart_wagonhr_assistant(user_request)
        
        assert result["status"] == "success"
        assert "operations" in result
        assert "total_count" in result
    
    @patch('tools.dynamic_wagonhr_api_agent.process_wagonhr_request')
    def test_leave_balance_formatting(self, mock_process):
        """Test formatting of leave balance response."""
        # Mock successful leave balance response
        mock_process.return_value = {
            "status": "success",
            "data": {
                "leave_balance": {
                    "casual": 5,
                    "sick": 3,
                    "annual": 10
                }
            }
        }
        
        user_request = "Check my leave balance"
        result = smart_wagonhr_assistant(user_request)
        
        assert result["status"] == "success"
        assert "formatted_response" in result
        assert "Casual: 5 days" in result["formatted_response"]
        assert "Sick: 3 days" in result["formatted_response"]
        assert "Annual: 10 days" in result["formatted_response"]
    
    @patch('tools.dynamic_wagonhr_api_agent.process_wagonhr_request')
    def test_leave_history_formatting(self, mock_process):
        """Test formatting of leave history response."""
        # Mock successful leave history response
        mock_process.return_value = {
            "status": "success",
            "data": [
                {
                    "startDate": "2024-01-15",
                    "endDate": "2024-01-17",
                    "leaveType": "casual",
                    "status": "approved"
                },
                {
                    "startDate": "2024-01-20",
                    "endDate": "2024-01-22",
                    "leaveType": "sick",
                    "status": "pending"
                }
            ]
        }
        
        user_request = "Show my leave history"
        result = smart_wagonhr_assistant(user_request)
        
        assert result["status"] == "success"
        assert "formatted_response" in result
        assert "casual from 2024-01-15 to 2024-01-17 - approved" in result["formatted_response"]
        assert "sick from 2024-01-20 to 2024-01-22 - pending" in result["formatted_response"]


class TestGetAvailableOperations:
    """Test cases for get_available_operations function."""
    
    def test_get_operations(self):
        """Test getting available operations."""
        result = get_available_operations()
        
        assert result["status"] == "success"
        assert "operations" in result
        assert "total_count" in result
        assert isinstance(result["operations"], dict)
        assert result["total_count"] > 0


class TestIntegration:
    """Integration test cases."""
    
    def test_end_to_end_leave_balance(self):
        """Test end-to-end leave balance request."""
        user_requests = [
            "How many leave days do I have?",
            "Check my leave balance",
            "What's my remaining vacation days?",
            "Show me my leave balance"
        ]
        
        for request in user_requests:
            # This should not raise an exception
            result = smart_wagonhr_assistant(request)
            assert "status" in result
    
    def test_end_to_end_leave_request(self):
        """Test end-to-end leave request submission."""
        user_requests = [
            "I want to apply for leave from 2024-01-15 to 2024-01-17",
            "Submit casual leave request for next week",
            "Apply for sick leave tomorrow"
        ]
        
        for request in user_requests:
            # This should not raise an exception
            result = smart_wagonhr_assistant(request)
            assert "status" in result
    
    def test_end_to_end_attendance(self):
        """Test end-to-end attendance recording."""
        user_requests = [
            "Record my attendance as present",
            "I want to check in",
            "Mark me present for today"
        ]
        
        for request in user_requests:
            # This should not raise an exception
            result = smart_wagonhr_assistant(request)
            assert "status" in result


if __name__ == "__main__":
    pytest.main([__file__])
