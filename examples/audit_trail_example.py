#!/usr/bin/env python
"""
Audit trail example.

This script demonstrates how to use the audit trail system in a tool.
"""

import os
import sys
import logging
import uuid
from typing import Dict, Any

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.audit_callbacks import (
    register_agent,
    get_agent_id,
    record_tool_result
)

# Set up logger
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def reset_password_tool(email: str, session_id: str, user_id: str) -> Dict[str, Any]:
    """Example tool that resets a user's password.
    
    Args:
        email: The email of the user whose password to reset.
        session_id: The ID of the current session.
        user_id: The ID of the current user.
        
    Returns:
        A dictionary containing the result of the operation.
    """
    logger.info(f"Resetting password for {email}")
    
    # In a real implementation, this would call the actual password reset logic
    # For this example, we'll just simulate a successful password reset
    
    # Simulate some processing time
    import time
    time.sleep(1)
    
    # Prepare the result
    result = {
        "success": True,
        "message": f"Password reset successfully for {email}",
        "temporary_password": "Temp123!",
        "entity_type": "password",
        "entity_id": email.split("@")[0]
    }
    
    # Record the tool result in the audit trail
    record_tool_result(
        tool_name="reset_password",
        agent_name="ITSM Agent",
        session_id=session_id,
        user_id=user_id,
        args={"email": email},
        result=result,
        status="success",
        entity_type="password",
        entity_id=email.split("@")[0]
    )
    
    return result


def create_ticket_tool(subject: str, description: str, session_id: str, user_id: str) -> Dict[str, Any]:
    """Example tool that creates a ticket in the ITSM system.
    
    Args:
        subject: The subject of the ticket.
        description: The description of the ticket.
        session_id: The ID of the current session.
        user_id: The ID of the current user.
        
    Returns:
        A dictionary containing the result of the operation.
    """
    logger.info(f"Creating ticket: {subject}")
    
    # In a real implementation, this would call the actual ticket creation logic
    # For this example, we'll just simulate a successful ticket creation
    
    # Simulate some processing time
    import time
    time.sleep(1)
    
    # Generate a ticket ID
    ticket_id = f"INC-{uuid.uuid4().hex[:6].upper()}"
    
    # Prepare the result
    result = {
        "success": True,
        "message": f"Ticket created successfully",
        "ticket_id": ticket_id,
        "entity_type": "ticket",
        "entity_id": ticket_id
    }
    
    # Record the tool result in the audit trail
    record_tool_result(
        tool_name="create_ticket",
        agent_name="ITSM Agent",
        session_id=session_id,
        user_id=user_id,
        args={"subject": subject, "description": description},
        result=result,
        status="success",
        entity_type="ticket",
        entity_id=ticket_id
    )
    
    return result


def main():
    """Run the example."""
    try:
        # Register the agent
        agent_id = register_agent(
            agent_name="ITSM Agent",
            agent_description="IT Service Management Agent",
            agent_config={
                "model": "claude-3-sonnet-20240229",
                "temperature": 0.7,
                "max_tokens": 4096
            }
        )
        logger.info(f"Agent registered with ID {agent_id}")
        
        # Create a session ID
        session_id = str(uuid.uuid4())
        logger.info(f"Session ID: {session_id}")
        
        # Set the user ID
        user_id = "<EMAIL>"
        logger.info(f"User ID: {user_id}")
        
        # Call the reset password tool
        reset_result = reset_password_tool(
            email="<EMAIL>",
            session_id=session_id,
            user_id=user_id
        )
        logger.info(f"Reset password result: {reset_result}")
        
        # Call the create ticket tool
        ticket_result = create_ticket_tool(
            subject="Password Reset",
            description="Reset <NAME_EMAIL>",
            session_id=session_id,
            user_id=user_id
        )
        logger.info(f"Create ticket result: {ticket_result}")
        
        logger.info("Example completed successfully")
    except Exception as e:
        logger.error(f"Error running example: {str(e)}")


if __name__ == "__main__":
    main()
