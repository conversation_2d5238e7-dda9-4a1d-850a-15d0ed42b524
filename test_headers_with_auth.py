#!/usr/bin/env python3
"""
Test script to demonstrate complete header flow including Authorization Bearer token.

This script shows how headers flow through the system from environment variables
to the final HTTP request, including the Authorization header with <PERSON><PERSON> token.
"""

import os
import sys
import logging

# Add the project root to the path
sys.path.append('.')

from utils.hr_api_client import leave_balance_client, attendance_client
from utils.hr_auth import hr_auth

# Set up logging to see the header flow
logging.basicConfig(level=logging.INFO, format='%(name)s - %(levelname)s - %(message)s')

def test_header_flow():
    """Test the complete header flow including Authorization."""
    
    print("🔍 Testing Complete Header Flow with Authorization")
    print("=" * 60)
    
    # 1. Set up environment headers
    print("\n1️⃣ Setting up environment headers:")
    os.environ["HR_TENANT_ID"] = "test-tenant-123"
    os.environ["HR_ENTITY_ID"] = "test-entity-456"
    os.environ["HR_ORGANIZATION_ID"] = "test-org-789"
    
    # Reinitialize client to pick up new environment variables
    from utils.hr_api_client import HRAPIClient
    test_client = HRAPIClient(auth_type="wagonhr")
    
    print(f"   Default headers: {test_client.default_headers}")
    
    # 2. Set tenant context programmatically
    print("\n2️⃣ Setting tenant context programmatically:")
    test_client.set_tenant_context(
        tenant_id="dynamic-tenant-999",
        entity_id="dynamic-entity-888"
    )
    print(f"   Updated headers: {test_client.default_headers}")
    
    # 3. Prepare custom headers for request
    print("\n3️⃣ Preparing custom headers for request:")
    custom_headers = {
        "requestId": "test-req-12345",
        "correlationId": "test-corr-67890",
        "userAgent": "HR-Test-Client/1.0"
    }
    print(f"   Custom headers: {custom_headers}")
    
    # 4. Show merged headers without auth
    print("\n4️⃣ Merged headers (without authentication):")
    merged_no_auth = test_client._merge_headers(custom_headers, include_auth=False)
    print(f"   Merged: {merged_no_auth}")
    
    # 5. Show complete headers with auth
    print("\n5️⃣ Complete headers (including authentication):")
    try:
        complete_headers = test_client.get_all_headers(custom_headers)
        print("   Complete headers:")
        for key, value in complete_headers.items():
            if key.lower() == 'authorization':
                # Mask the token for security
                if value.startswith('Bearer '):
                    token_part = value[7:]  # Remove 'Bearer ' prefix
                    masked_value = f"Bearer ***{token_part[-10:] if len(token_part) > 10 else '***'}"
                else:
                    masked_value = "***masked***"
                print(f"   ✓ {key}: {masked_value}")
            else:
                print(f"   ✓ {key}: {value}")
    except Exception as e:
        print(f"   ⚠️  Auth headers not available: {e}")
        print("   (This is expected if WagonHR credentials are not configured)")
    
    # 6. Show the actual flow during API call
    print("\n6️⃣ Simulating API call flow:")
    print("   Step 1: Client merges default + custom headers")
    print("   Step 2: Auth manager adds Authorization header")
    print("   Step 3: Final headers sent to HTTP request")
    
    # 7. Show what the final HTTP request would look like
    print("\n7️⃣ Final HTTP request structure:")
    print("   HTTP Method: GET")
    print("   URL: https://dev-api-wagonhr.mouritech.net/api/hrms/leave-attendance/employee-leaves")
    print("   Headers:")
    print("     Authorization: Bearer <wagonhr-access-token>")
    print("     Content-Type: application/json")
    print("     tenantId: dynamic-tenant-999")
    print("     entityId: dynamic-entity-888")
    print("     requestId: test-req-12345")
    print("     correlationId: test-corr-67890")
    print("     userAgent: HR-Test-Client/1.0")
    print("   Query Params: ?email=<EMAIL>")

def test_actual_api_call():
    """Test an actual API call to see headers in action."""
    
    print("\n🚀 Testing Actual API Call")
    print("=" * 40)
    
    # Set up headers
    custom_headers = {
        "tenantId": "api-test-tenant",
        "entityId": "api-test-entity",
        "requestId": "api-test-12345"
    }
    
    print("Making API call with custom headers...")
    print(f"Custom headers: {custom_headers}")
    
    try:
        # This will trigger the complete header flow
        result = leave_balance_client.get_leave_balance(
            email="<EMAIL>",
            headers=custom_headers
        )
        print(f"✅ API call successful: {result}")
    except Exception as e:
        print(f"⚠️  API call failed (expected): {e}")
        print("   This is normal if WagonHR API is not accessible or credentials are not set")

def show_auth_header_details():
    """Show details about how Authorization headers are constructed."""
    
    print("\n🔐 Authorization Header Details")
    print("=" * 40)
    
    print("WagonHR Authentication Flow:")
    print("1. Username/password sent to /auth/user/v3/login")
    print("2. Response contains access token")
    print("3. Token stored in hr_auth.wagonhr_access_token")
    print("4. Authorization header: 'Bearer <access_token>'")
    
    print("\nMicrosoft Authentication Flow:")
    print("1. Frontend provides Microsoft access token")
    print("2. Token stored in hr_auth.ms_token_from_frontend")
    print("3. Authorization header: 'Bearer <ms_access_token>'")
    
    print("\nHeader Priority (last wins):")
    print("1. Default headers (environment)")
    print("2. Tenant context headers")
    print("3. Per-request custom headers")
    print("4. Authentication headers (Authorization, Content-Type)")

if __name__ == "__main__":
    test_header_flow()
    test_actual_api_call()
    show_auth_header_details()
    
    print("\n" + "=" * 60)
    print("✅ Header flow test completed!")
    print("💡 Check the logs above to see how headers are merged and sent.")
