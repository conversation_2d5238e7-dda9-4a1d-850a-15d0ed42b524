"""
AWS OpenSearch Vector Database Tool for HR Policy Management.

This module provides secure integration with AWS OpenSearch for policy-related queries
using vector embeddings and semantic search capabilities.
"""

import logging
import os
import json
import boto3
import requests
from typing import Dict, Any, Optional, List
from datetime import datetime
from requests.auth import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from requests_aws4auth import <PERSON><PERSON><PERSON><PERSON><PERSON>
import numpy as np
from sentence_transformers import SentenceTransformer

# Set up logger
logger = logging.getLogger(__name__)

class AWSOpenSearchPolicyClient:
    """AWS OpenSearch client for HR policy vector search."""
    
    def __init__(self):
        """Initialize the AWS OpenSearch client with secure configuration."""
        self.region = os.environ.get("AWS_REGION", "us-east-1")
        self.opensearch_endpoint = os.environ.get("OPENSEARCH_ENDPOINT")
        self.opensearch_index = os.environ.get("OPENSEARCH_POLICY_INDEX", "hr-policies")
        self.embedding_model_name = os.environ.get("EMBEDDING_MODEL", "all-MiniLM-L6-v2")
        
        # Initialize AWS credentials
        self.aws_access_key = os.environ.get("AWS_ACCESS_KEY_ID")
        self.aws_secret_key = os.environ.get("AWS_SECRET_ACCESS_KEY")
        self.aws_session_token = os.environ.get("AWS_SESSION_TOKEN")
        
        # Initialize embedding model
        try:
            self.embedding_model = SentenceTransformer(self.embedding_model_name)
            logger.info(f"Loaded embedding model: {self.embedding_model_name}")
        except Exception as e:
            logger.warning(f"Failed to load embedding model: {e}. Using mock embeddings.")
            self.embedding_model = None
        
        # Initialize AWS authentication
        self._setup_aws_auth()
        
        # Validate configuration
        self._validate_configuration()
    
    def _setup_aws_auth(self):
        """Set up AWS authentication for OpenSearch."""
        try:
            if self.aws_access_key and self.aws_secret_key:
                # Use explicit credentials
                self.aws_auth = AWS4Auth(
                    self.aws_access_key,
                    self.aws_secret_key,
                    self.region,
                    'es',
                    session_token=self.aws_session_token
                )
                logger.info("Using explicit AWS credentials for OpenSearch authentication")
            else:
                # Use IAM role or default credentials
                session = boto3.Session()
                credentials = session.get_credentials()
                if credentials:
                    self.aws_auth = AWS4Auth(
                        credentials.access_key,
                        credentials.secret_key,
                        self.region,
                        'es',
                        session_token=credentials.token
                    )
                    logger.info("Using IAM role/default credentials for OpenSearch authentication")
                else:
                    logger.error("No AWS credentials found")
                    self.aws_auth = None
        except Exception as e:
            logger.error(f"Failed to setup AWS authentication: {e}")
            self.aws_auth = None
    
    def _validate_configuration(self):
        """Validate OpenSearch configuration."""
        if not self.opensearch_endpoint:
            logger.warning("OPENSEARCH_ENDPOINT not configured. Using mock mode.")
            self.mock_mode = True
        elif not self.aws_auth:
            logger.warning("AWS authentication not configured. Using mock mode.")
            self.mock_mode = True
        else:
            self.mock_mode = False
            logger.info(f"OpenSearch configured: {self.opensearch_endpoint}")
    
    def _generate_embedding(self, text: str) -> List[float]:
        """Generate vector embedding for text."""
        try:
            if self.embedding_model:
                embedding = self.embedding_model.encode(text)
                return embedding.tolist()
            else:
                # Mock embedding for development
                return [0.1] * 384  # Standard dimension for all-MiniLM-L6-v2
        except Exception as e:
            logger.error(f"Failed to generate embedding: {e}")
            return [0.1] * 384
    
    def _make_opensearch_request(self, method: str, path: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """Make authenticated request to OpenSearch."""
        if self.mock_mode:
            return self._mock_opensearch_response(method, path, data)
        
        try:
            url = f"{self.opensearch_endpoint}{path}"
            headers = {"Content-Type": "application/json"}
            
            if method.upper() == "GET":
                response = requests.get(url, auth=self.aws_auth, headers=headers)
            elif method.upper() == "POST":
                response = requests.post(url, auth=self.aws_auth, headers=headers, 
                                       json=data if data else {})
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"OpenSearch request failed: {e}")
            return {"error": str(e)}
        except Exception as e:
            logger.error(f"Unexpected error in OpenSearch request: {e}")
            return {"error": str(e)}
    
    def _mock_opensearch_response(self, method: str, path: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """Generate mock OpenSearch response for development."""
        logger.info(f"Mock OpenSearch {method} request to {path}")
        
        if "_search" in path:
            # Mock search response
            query_text = ""
            if data and "query" in data:
                if "bool" in data["query"] and "must" in data["query"]["bool"]:
                    for clause in data["query"]["bool"]["must"]:
                        if "match" in clause:
                            query_text = list(clause["match"].values())[0]
                            break
            
            return {
                "took": 5,
                "timed_out": False,
                "hits": {
                    "total": {"value": 2, "relation": "eq"},
                    "max_score": 0.95,
                    "hits": [
                        {
                            "_index": self.opensearch_index,
                            "_id": "POL-001",
                            "_score": 0.95,
                            "_source": {
                                "policy_id": "POL-001",
                                "title": "Annual Leave Policy",
                                "category": "Leave",
                                "subcategory": "Annual Leave",
                                "content": "All full-time employees are entitled to 20 days of annual leave per year...",
                                "summary": "Policy governing annual leave entitlements, eligibility, and procedures.",
                                "effective_date": "2023-01-01",
                                "last_updated": "2023-01-01",
                                "embedding": [0.1] * 384
                            }
                        },
                        {
                            "_index": self.opensearch_index,
                            "_id": "POL-002",
                            "_score": 0.87,
                            "_source": {
                                "policy_id": "POL-002",
                                "title": "Sick Leave Policy",
                                "category": "Leave",
                                "subcategory": "Sick Leave",
                                "content": "All employees are entitled to 15 days of sick leave per year...",
                                "summary": "Policy governing sick leave entitlements and procedures.",
                                "effective_date": "2023-01-01",
                                "last_updated": "2023-01-01",
                                "embedding": [0.1] * 384
                            }
                        }
                    ]
                }
            }
        else:
            return {"acknowledged": True}
    
    def search_policies_vector(self, query: str, top_k: int = 5, category_filter: Optional[str] = None) -> Dict[str, Any]:
        """Search policies using vector similarity."""
        try:
            logger.info(f"Searching policies with vector query: {query}")
            
            # Generate query embedding
            query_embedding = self._generate_embedding(query)
            
            # Build OpenSearch query
            search_query = {
                "size": top_k,
                "query": {
                    "bool": {
                        "must": [
                            {
                                "knn": {
                                    "embedding": {
                                        "vector": query_embedding,
                                        "k": top_k
                                    }
                                }
                            }
                        ]
                    }
                },
                "_source": {
                    "excludes": ["embedding"]  # Exclude embedding from response
                }
            }
            
            # Add category filter if specified
            if category_filter:
                search_query["query"]["bool"]["filter"] = [
                    {"term": {"category.keyword": category_filter}}
                ]
            
            # Execute search
            response = self._make_opensearch_request("POST", f"/{self.opensearch_index}/_search", search_query)
            
            if "error" in response:
                return {
                    "status": "error",
                    "message": f"OpenSearch error: {response['error']}",
                    "results": []
                }
            
            # Process results
            results = []
            if "hits" in response and "hits" in response["hits"]:
                for hit in response["hits"]["hits"]:
                    source = hit["_source"]
                    results.append({
                        "policy_id": source.get("policy_id"),
                        "title": source.get("title"),
                        "category": source.get("category"),
                        "subcategory": source.get("subcategory", ""),
                        "content": source.get("content", "")[:500] + "...",  # Truncate content
                        "summary": source.get("summary", ""),
                        "relevance_score": hit["_score"],
                        "effective_date": source.get("effective_date"),
                        "last_updated": source.get("last_updated")
                    })
            
            logger.info(f"Found {len(results)} policies for vector query: {query}")
            
            return {
                "status": "success",
                "message": f"Found {len(results)} policies matching your query",
                "query": query,
                "results": results,
                "search_type": "vector_similarity"
            }
            
        except Exception as e:
            logger.error(f"Error in vector policy search: {e}")
            return {
                "status": "error",
                "message": f"Error searching policies: {str(e)}",
                "results": []
            }
    
    def search_policies_hybrid(self, query: str, top_k: int = 5, category_filter: Optional[str] = None) -> Dict[str, Any]:
        """Search policies using hybrid (vector + keyword) search."""
        try:
            logger.info(f"Searching policies with hybrid query: {query}")
            
            # Generate query embedding
            query_embedding = self._generate_embedding(query)
            
            # Build hybrid search query
            search_query = {
                "size": top_k,
                "query": {
                    "bool": {
                        "should": [
                            {
                                "knn": {
                                    "embedding": {
                                        "vector": query_embedding,
                                        "k": top_k,
                                        "boost": 0.7
                                    }
                                }
                            },
                            {
                                "multi_match": {
                                    "query": query,
                                    "fields": ["title^2", "content", "summary"],
                                    "boost": 0.3
                                }
                            }
                        ],
                        "minimum_should_match": 1
                    }
                },
                "_source": {
                    "excludes": ["embedding"]
                }
            }
            
            # Add category filter if specified
            if category_filter:
                search_query["query"]["bool"]["filter"] = [
                    {"term": {"category.keyword": category_filter}}
                ]
            
            # Execute search
            response = self._make_opensearch_request("POST", f"/{self.opensearch_index}/_search", search_query)
            
            if "error" in response:
                return {
                    "status": "error",
                    "message": f"OpenSearch error: {response['error']}",
                    "results": []
                }
            
            # Process results (same as vector search)
            results = []
            if "hits" in response and "hits" in response["hits"]:
                for hit in response["hits"]["hits"]:
                    source = hit["_source"]
                    results.append({
                        "policy_id": source.get("policy_id"),
                        "title": source.get("title"),
                        "category": source.get("category"),
                        "subcategory": source.get("subcategory", ""),
                        "content": source.get("content", "")[:500] + "...",
                        "summary": source.get("summary", ""),
                        "relevance_score": hit["_score"],
                        "effective_date": source.get("effective_date"),
                        "last_updated": source.get("last_updated")
                    })
            
            logger.info(f"Found {len(results)} policies for hybrid query: {query}")
            
            return {
                "status": "success",
                "message": f"Found {len(results)} policies matching your query",
                "query": query,
                "results": results,
                "search_type": "hybrid_search"
            }
            
        except Exception as e:
            logger.error(f"Error in hybrid policy search: {e}")
            return {
                "status": "error",
                "message": f"Error searching policies: {str(e)}",
                "results": []
            }

# Initialize the OpenSearch client
opensearch_client = AWSOpenSearchPolicyClient()

# Tool Functions for Agent Integration

def search_hr_policies_vector(
    query: str,
    top_k: int = 5,
    category_filter: Optional[str] = None,
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Search HR policies using vector similarity search.

    This function provides semantic search capabilities for HR policies using
    vector embeddings stored in AWS OpenSearch.

    Args:
        query: The search query (natural language)
        top_k: Number of results to return (default: 5)
        category_filter: Optional category filter (e.g., "Leave", "Attendance")
        tool_context: Optional tool context for session management

    Returns:
        Dictionary containing search results and metadata
    """
    try:
        logger.info(f"=== HR POLICY VECTOR SEARCH ===")
        logger.info(f"Query: {query}")
        logger.info(f"Top K: {top_k}")
        logger.info(f"Category Filter: {category_filter}")

        # Perform vector search
        result = opensearch_client.search_policies_vector(
            query=query,
            top_k=top_k,
            category_filter=category_filter
        )

        # Store results in session if context provided
        if tool_context and result.get("status") == "success":
            tool_context.state["last_policy_search"] = {
                "query": query,
                "results": result.get("results", []),
                "search_type": "vector",
                "timestamp": datetime.now().isoformat()
            }
            logger.info("Policy search results stored in session state")

        logger.info(f"Vector search completed: {result.get('status')}")
        return result

    except Exception as e:
        logger.error(f"Error in HR policy vector search: {e}")
        return {
            "status": "error",
            "message": f"Error searching HR policies: {str(e)}",
            "results": []
        }

def search_hr_policies_hybrid(
    query: str,
    top_k: int = 5,
    category_filter: Optional[str] = None,
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Search HR policies using hybrid (vector + keyword) search.

    This function combines semantic vector search with traditional keyword search
    for more comprehensive results.

    Args:
        query: The search query (natural language)
        top_k: Number of results to return (default: 5)
        category_filter: Optional category filter (e.g., "Leave", "Attendance")
        tool_context: Optional tool context for session management

    Returns:
        Dictionary containing search results and metadata
    """
    try:
        logger.info(f"=== HR POLICY HYBRID SEARCH ===")
        logger.info(f"Query: {query}")
        logger.info(f"Top K: {top_k}")
        logger.info(f"Category Filter: {category_filter}")

        # Perform hybrid search
        result = opensearch_client.search_policies_hybrid(
            query=query,
            top_k=top_k,
            category_filter=category_filter
        )

        # Store results in session if context provided
        if tool_context and result.get("status") == "success":
            tool_context.state["last_policy_search"] = {
                "query": query,
                "results": result.get("results", []),
                "search_type": "hybrid",
                "timestamp": datetime.now().isoformat()
            }
            logger.info("Policy search results stored in session state")

        logger.info(f"Hybrid search completed: {result.get('status')}")
        return result

    except Exception as e:
        logger.error(f"Error in HR policy hybrid search: {e}")
        return {
            "status": "error",
            "message": f"Error searching HR policies: {str(e)}",
            "results": []
        }

def get_policy_by_id_opensearch(
    policy_id: str,
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Retrieve a specific HR policy by ID from OpenSearch.

    Args:
        policy_id: The unique identifier of the policy
        tool_context: Optional tool context for session management

    Returns:
        Dictionary containing the policy details
    """
    try:
        logger.info(f"=== RETRIEVING POLICY BY ID ===")
        logger.info(f"Policy ID: {policy_id}")

        # Get policy by ID
        response = opensearch_client._make_opensearch_request(
            "GET",
            f"/{opensearch_client.opensearch_index}/_doc/{policy_id}"
        )

        if "error" in response:
            return {
                "status": "error",
                "message": f"Policy {policy_id} not found: {response['error']}",
                "policy": None
            }

        if response.get("found"):
            source = response["_source"]
            policy = {
                "policy_id": source.get("policy_id"),
                "title": source.get("title"),
                "category": source.get("category"),
                "subcategory": source.get("subcategory", ""),
                "content": source.get("content"),
                "summary": source.get("summary", ""),
                "effective_date": source.get("effective_date"),
                "last_updated": source.get("last_updated")
            }

            # Store in session if context provided
            if tool_context:
                tool_context.state["last_policy_retrieved"] = {
                    "policy_id": policy_id,
                    "policy": policy,
                    "timestamp": datetime.now().isoformat()
                }
                logger.info("Policy details stored in session state")

            logger.info(f"Policy {policy_id} retrieved successfully")
            return {
                "status": "success",
                "message": f"Policy {policy_id} retrieved successfully",
                "policy": policy
            }
        else:
            return {
                "status": "error",
                "message": f"Policy {policy_id} not found",
                "policy": None
            }

    except Exception as e:
        logger.error(f"Error retrieving policy {policy_id}: {e}")
        return {
            "status": "error",
            "message": f"Error retrieving policy: {str(e)}",
            "policy": None
        }

def get_policies_by_category_opensearch(
    category: str,
    top_k: int = 10,
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Retrieve HR policies by category from OpenSearch.

    Args:
        category: The policy category (e.g., "Leave", "Attendance")
        top_k: Maximum number of policies to return
        tool_context: Optional tool context for session management

    Returns:
        Dictionary containing the policies in the specified category
    """
    try:
        logger.info(f"=== RETRIEVING POLICIES BY CATEGORY ===")
        logger.info(f"Category: {category}")
        logger.info(f"Top K: {top_k}")

        # Build category search query
        search_query = {
            "size": top_k,
            "query": {
                "term": {
                    "category.keyword": category
                }
            },
            "_source": {
                "excludes": ["embedding"]
            },
            "sort": [
                {"last_updated": {"order": "desc"}},
                {"_score": {"order": "desc"}}
            ]
        }

        # Execute search
        response = opensearch_client._make_opensearch_request(
            "POST",
            f"/{opensearch_client.opensearch_index}/_search",
            search_query
        )

        if "error" in response:
            return {
                "status": "error",
                "message": f"Error retrieving policies for category {category}: {response['error']}",
                "policies": []
            }

        # Process results
        policies = []
        if "hits" in response and "hits" in response["hits"]:
            for hit in response["hits"]["hits"]:
                source = hit["_source"]
                policies.append({
                    "policy_id": source.get("policy_id"),
                    "title": source.get("title"),
                    "category": source.get("category"),
                    "subcategory": source.get("subcategory", ""),
                    "summary": source.get("summary", ""),
                    "effective_date": source.get("effective_date"),
                    "last_updated": source.get("last_updated")
                })

        # Store in session if context provided
        if tool_context:
            tool_context.state["last_category_search"] = {
                "category": category,
                "policies": policies,
                "timestamp": datetime.now().isoformat()
            }
            logger.info("Category search results stored in session state")

        logger.info(f"Retrieved {len(policies)} policies for category: {category}")

        return {
            "status": "success",
            "message": f"Retrieved {len(policies)} policies in category: {category}",
            "category": category,
            "policies": policies
        }

    except Exception as e:
        logger.error(f"Error retrieving policies by category {category}: {e}")
        return {
            "status": "error",
            "message": f"Error retrieving policies by category: {str(e)}",
            "policies": []
        }
