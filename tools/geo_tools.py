"""
Geo-fencing and Location tools module.

This module provides tools for location verification, geo-fencing,
and location-based attendance verification.
"""

import logging
import os
import json
import datetime
import math
from typing import Dict, Any, Optional, List, Tuple

from google.adk.tools.tool_context import ToolContext

# Set up logger
logger = logging.getLogger(__name__)

# Mock location database for development and testing
# In a production environment, this would be replaced with actual API calls to the HRMS
_location_records_db = {}
_next_location_id = 1000

# Office locations with their coordinates for geo-fencing
OFFICE_LOCATIONS = {
    "New York Office": {"lat": 40.7128, "lng": -74.0060, "radius": 0.5, "address": "123 Broadway, New York, NY 10001"},
    "San Francisco Office": {"lat": 37.7749, "lng": -122.4194, "radius": 0.5, "address": "456 Market St, San Francisco, CA 94105"},
    "Bangalore Office": {"lat": 12.9716, "lng": 77.5946, "radius": 0.5, "address": "789 MG Road, Bangalore, Karnataka 560001"},
    "London Office": {"lat": 51.5074, "lng": -0.1278, "radius": 0.5, "address": "10 Downing Street, London SW1A 2AA"},
    "Singapore Office": {"lat": 1.3521, "lng": 103.8198, "radius": 0.5, "address": "1 Raffles Place, Singapore 048616"}
}


def calculate_distance(lat1: float, lng1: float, lat2: float, lng2: float) -> float:
    """Calculates the Haversine distance between two points in kilometers.

    Args:
        lat1: Latitude of point 1
        lng1: Longitude of point 1
        lat2: Latitude of point 2
        lng2: Longitude of point 2

    Returns:
        Distance in kilometers
    """
    # Convert latitude and longitude from degrees to radians
    lat1_rad = math.radians(lat1)
    lng1_rad = math.radians(lng1)
    lat2_rad = math.radians(lat2)
    lng2_rad = math.radians(lng2)
    
    # Haversine formula
    dlat = lat2_rad - lat1_rad
    dlng = lng2_rad - lng1_rad
    a = math.sin(dlat/2)**2 + math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlng/2)**2
    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
    radius = 6371  # Earth's radius in kilometers
    
    return radius * c


def verify_location(
    employee_email,
    latitude,
    longitude,
    timestamp = None,
    office_name = None,
    tool_context = None
) -> Dict[str, Any]:
    """Verifies if an employee's location is within the geo-fence of an office.

    Args:
        employee_email: Email of the employee
        latitude: Latitude of the employee's location
        longitude: Longitude of the employee's location
        timestamp: Optional timestamp of the location check
        office_name: Optional specific office to check against
        tool_context: Tool context for accessing session state

    Returns:
        Dictionary containing operation status and location verification details
    """
    global _next_location_id
    
    logger.info(f"Verifying location for {employee_email} at coordinates ({latitude}, {longitude})")
    
    # Check if employee exists in HRMS
    from tools.hrms import _employees_db
    if employee_email not in _employees_db:
        logger.warning(f"Employee not found: {employee_email}")
        return {
            "status": "error",
            "message": f"Employee with email {employee_email} not found in HRMS"
        }
    
    # Get employee information
    employee = _employees_db[employee_email]
    
    # Use current time if timestamp not provided
    if not timestamp:
        current_time = datetime.datetime.now()
        timestamp = current_time.isoformat()
    else:
        try:
            # Validate timestamp format
            current_time = datetime.datetime.fromisoformat(timestamp)
        except ValueError:
            logger.warning(f"Invalid timestamp format: {timestamp}")
            return {
                "status": "error",
                "message": f"Invalid timestamp format. Please use ISO format (YYYY-MM-DDTHH:MM:SS)."
            }
    
    # If office_name provided, check only that office
    if office_name:
        if office_name not in OFFICE_LOCATIONS:
            logger.warning(f"Office not found: {office_name}")
            return {
                "status": "error",
                "message": f"Office {office_name} not found. Valid offices are: {', '.join(OFFICE_LOCATIONS.keys())}"
            }
        
        offices_to_check = {office_name: OFFICE_LOCATIONS[office_name]}
    else:
        # Check all offices
        offices_to_check = OFFICE_LOCATIONS
    
    # Check distance to each office
    nearest_office = None
    nearest_distance = float('inf')
    within_geofence = False
    
    for name, office in offices_to_check.items():
        distance = calculate_distance(latitude, longitude, office["lat"], office["lng"])
        
        if distance < nearest_distance:
            nearest_distance = distance
            nearest_office = name
        
        if distance <= office["radius"]:
            within_geofence = True
            break
    
    # Generate location record ID
    location_id = f"LOC-{_next_location_id}"
    _next_location_id += 1
    
    # Create the location record
    location_record = {
        "id": location_id,
        "employee_email": employee_email,
        "employee_name": employee.get("full_name"),
        "latitude": latitude,
        "longitude": longitude,
        "timestamp": timestamp,
        "nearest_office": nearest_office,
        "distance_to_nearest_office": nearest_distance,
        "within_geofence": within_geofence,
        "office_checked": office_name if office_name else "all",
        "recorded_at": datetime.datetime.now().isoformat()
    }
    
    # Store in mock database
    _location_records_db[location_id] = location_record
    
    # Store in session state if context provided
    if tool_context:
        tool_context.state["current_location_record"] = location_record
    
    logger.info(f"Location verified for {employee_email}: within geofence = {within_geofence}")
    
    # If within geofence, also record attendance
    if within_geofence:
        try:
            from tools.attendance_tools import record_attendance
            attendance_result = record_attendance(
                employee_email=employee_email,
                status="present",
                timestamp=timestamp,
                location={"lat": latitude, "lng": longitude, "office": nearest_office},
                notes=f"Auto-recorded via geo-fencing at {nearest_office}",
                tool_context=tool_context
            )
            
            if attendance_result["status"] == "success":
                location_record["attendance_recorded"] = True
                location_record["attendance_id"] = attendance_result["attendance_id"]
                _location_records_db[location_id] = location_record
        except ImportError:
            logger.warning("Could not import attendance_tools module")
    
    return {
        "status": "success",
        "message": f"Location verified successfully",
        "location_id": location_id,
        "within_geofence": within_geofence,
        "nearest_office": nearest_office,
        "distance_to_nearest_office": nearest_distance,
        "location_record": location_record
    }


def get_office_locations(
    office_name = None,
    tool_context = None
) -> Dict[str, Any]:
    """Retrieves office location information.

    Args:
        office_name: Optional specific office to retrieve
        tool_context: Tool context for accessing session state

    Returns:
        Dictionary containing operation status and office location details
    """
    logger.info(f"Retrieving office location information")
    
    # If office_name provided, return only that office
    if office_name:
        if office_name not in OFFICE_LOCATIONS:
            logger.warning(f"Office not found: {office_name}")
            return {
                "status": "error",
                "message": f"Office {office_name} not found. Valid offices are: {', '.join(OFFICE_LOCATIONS.keys())}"
            }
        
        office_info = OFFICE_LOCATIONS[office_name]
        
        # Store in session state if context provided
        if tool_context:
            tool_context.state["office_location"] = {
                "name": office_name,
                **office_info
            }
        
        logger.info(f"Office location information retrieved for {office_name}")
        
        return {
            "status": "success",
            "message": f"Office location information retrieved successfully",
            "office": {
                "name": office_name,
                **office_info
            }
        }
    else:
        # Return all offices
        offices = []
        for name, info in OFFICE_LOCATIONS.items():
            offices.append({
                "name": name,
                **info
            })
        
        # Store in session state if context provided
        if tool_context:
            tool_context.state["office_locations"] = offices
        
        logger.info(f"Retrieved information for {len(offices)} office locations")
        
        return {
            "status": "success",
            "message": f"Office location information retrieved successfully",
            "offices": offices
        }


def update_employee_location(
    employee_email,
    office_name,
    remote_work = False,
    effective_date = None,
    tool_context = None
) -> Dict[str, Any]:
    """Updates an employee's assigned office location.

    Args:
        employee_email: Email of the employee
        office_name: Name of the office to assign
        remote_work: Whether the employee is working remotely
        effective_date: Optional effective date of the change (YYYY-MM-DD)
        tool_context: Tool context for accessing session state

    Returns:
        Dictionary containing operation status and updated employee details
    """
    logger.info(f"Updating location for {employee_email} to {office_name}")
    
    # Check if employee exists in HRMS
    from tools.hrms import _employees_db
    if employee_email not in _employees_db:
        logger.warning(f"Employee not found: {employee_email}")
        return {
            "status": "error",
            "message": f"Employee with email {employee_email} not found in HRMS"
        }
    
    # Check if office exists
    if office_name not in OFFICE_LOCATIONS and not remote_work:
        logger.warning(f"Office not found: {office_name}")
        return {
            "status": "error",
            "message": f"Office {office_name} not found. Valid offices are: {', '.join(OFFICE_LOCATIONS.keys())}"
        }
    
    # Parse effective date if provided
    if effective_date:
        try:
            effective_date_obj = datetime.datetime.strptime(effective_date, "%Y-%m-%d").date()
        except ValueError:
            logger.warning(f"Invalid date format: {effective_date}")
            return {
                "status": "error",
                "message": f"Invalid date format. Please use YYYY-MM-DD format."
            }
    else:
        effective_date = datetime.date.today().isoformat()
    
    # Get employee information
    employee = _employees_db[employee_email]
    
    # Store previous location
    previous_location = employee.get("location")
    previous_remote_work = employee.get("remote_work", False)
    
    # Update employee location
    employee["location"] = office_name
    employee["remote_work"] = remote_work
    employee["location_updated_at"] = datetime.datetime.now().isoformat()
    employee["location_effective_date"] = effective_date
    
    # Add location history if it doesn't exist
    if "location_history" not in employee:
        employee["location_history"] = []
    
    # Add to location history
    employee["location_history"].append({
        "previous_location": previous_location,
        "previous_remote_work": previous_remote_work,
        "new_location": office_name,
        "new_remote_work": remote_work,
        "updated_at": datetime.datetime.now().isoformat(),
        "effective_date": effective_date
    })
    
    # Update in mock database
    _employees_db[employee_email] = employee
    
    # Store in session state if context provided
    if tool_context:
        tool_context.state["updated_employee"] = employee
    
    logger.info(f"Location updated successfully for {employee_email} to {office_name}")
    
    return {
        "status": "success",
        "message": f"Employee location updated successfully",
        "employee_email": employee_email,
        "employee_name": employee.get("full_name"),
        "previous_location": previous_location,
        "previous_remote_work": previous_remote_work,
        "new_location": office_name,
        "new_remote_work": remote_work,
        "effective_date": effective_date
    }


def get_location_history(
    employee_email,
    start_date = None,
    end_date = None,
    tool_context = None
) -> Dict[str, Any]:
    """Retrieves location history for an employee.

    Args:
        employee_email: Email of the employee
        start_date: Optional start date filter (YYYY-MM-DD)
        end_date: Optional end date filter (YYYY-MM-DD)
        tool_context: Tool context for accessing session state

    Returns:
        Dictionary containing operation status and location history details
    """
    logger.info(f"Retrieving location history for {employee_email}")
    
    # Check if employee exists in HRMS
    from tools.hrms import _employees_db
    if employee_email not in _employees_db:
        logger.warning(f"Employee not found: {employee_email}")
        return {
            "status": "error",
            "message": f"Employee with email {employee_email} not found in HRMS"
        }
    
    # Get employee information
    employee = _employees_db[employee_email]
    
    # Get location history
    location_history = employee.get("location_history", [])
    
    # Parse date filters if provided
    start_date_obj = None
    end_date_obj = None
    
    if start_date:
        try:
            start_date_obj = datetime.datetime.strptime(start_date, "%Y-%m-%d").date()
        except ValueError:
            logger.warning(f"Invalid start date format: {start_date}")
            return {
                "status": "error",
                "message": f"Invalid start date format. Please use YYYY-MM-DD format."
            }
    
    if end_date:
        try:
            end_date_obj = datetime.datetime.strptime(end_date, "%Y-%m-%d").date()
        except ValueError:
            logger.warning(f"Invalid end date format: {end_date}")
            return {
                "status": "error",
                "message": f"Invalid end date format. Please use YYYY-MM-DD format."
            }
    
    # Filter by date if provided
    if start_date_obj or end_date_obj:
        filtered_history = []
        for entry in location_history:
            entry_date = datetime.datetime.strptime(entry["effective_date"], "%Y-%m-%d").date()
            
            if start_date_obj and entry_date < start_date_obj:
                continue
            
            if end_date_obj and entry_date > end_date_obj:
                continue
            
            filtered_history.append(entry)
        
        location_history = filtered_history
    
    # Store in session state if context provided
    if tool_context:
        tool_context.state["location_history"] = location_history
    
    logger.info(f"Retrieved {len(location_history)} location history entries for {employee_email}")
    
    return {
        "status": "success",
        "message": f"Location history retrieved successfully",
        "employee_email": employee_email,
        "employee_name": employee.get("full_name"),
        "current_location": employee.get("location"),
        "current_remote_work": employee.get("remote_work", False),
        "location_history": location_history
    }
