"""
Flexible WagonHR API Agent.

This module provides a flexible agent that can call any WagonHR API endpoint
by taking API details and dynamically constructing requests without hardcoding.
"""

import logging
import json
import requests
from typing import Dict, Any, Optional, List, Union
from datetime import datetime
import re
from utils.hr_auth import hr_auth

# Set up logger
logger = logging.getLogger(__name__)

class FlexibleWagonHRAgent:
    """Flexible agent that can call any WagonHR API with dynamic request construction."""
    
    def __init__(self):
        """Initialize the flexible WagonHR agent."""
        self.base_url = "https://dev-api-wagonhr.mouritech.net"

        # Define available API endpoints with their details
        self.api_endpoints = {
            "leave_balance": {
                "path": "/api/hrms/leave/balance",
                "method": "GET",
                "description": "Get employee leave balance",
                "parameters": [],
                "response_format": "leave balance data"
            },
            "leave_request": {
                "path": "/api/hrms/leave/request",
                "method": "POST",
                "description": "Submit a leave request",
                "parameters": ["startDate", "endDate", "leaveType", "reason", "halfDay"],
                "response_format": "leave request confirmation"
            },
            "leave_history": {
                "path": "/api/hrms/leave/history",
                "method": "GET",
                "description": "Get employee leave history",
                "parameters": ["startDate", "endDate", "limit"],
                "response_format": "leave history records"
            },
            "attendance_record": {
                "path": "/api/hrms/attendance/record",
                "method": "POST",
                "description": "Record employee attendance",
                "parameters": ["status", "location", "timestamp", "notes"],
                "response_format": "attendance record confirmation"
            },
            "attendance_report": {
                "path": "/api/hrms/attendance/report",
                "method": "GET",
                "description": "Get employee attendance report",
                "parameters": ["startDate", "endDate", "format"],
                "response_format": "attendance report data"
            },
            "employee_profile": {
                "path": "/api/hrms/employee/profile",
                "method": "GET",
                "description": "Get employee profile information",
                "parameters": [],
                "response_format": "employee profile data"
            }
        }

        # Try to load additional endpoints from Swagger/OpenAPI if available
        self._load_swagger_endpoints()

    def _load_swagger_endpoints(self):
        """Load additional API endpoints from Swagger/OpenAPI documentation."""
        try:
            # Common Swagger/OpenAPI documentation URLs
            swagger_urls = [
                f"{self.base_url}/swagger-ui.html",
                f"{self.base_url}/api-docs",
                f"{self.base_url}/v3/api-docs",
                f"{self.base_url}/swagger.json",
                f"{self.base_url}/openapi.json"
            ]

            for swagger_url in swagger_urls:
                try:
                    logger.info(f"Attempting to load Swagger documentation from: {swagger_url}")
                    response = requests.get(swagger_url, timeout=10)

                    if response.status_code == 200:
                        swagger_data = response.json()
                        self._parse_swagger_endpoints(swagger_data)
                        logger.info(f"Successfully loaded endpoints from Swagger: {swagger_url}")
                        break

                except Exception as e:
                    logger.debug(f"Failed to load from {swagger_url}: {e}")
                    continue

        except Exception as e:
            logger.warning(f"Could not load Swagger endpoints: {e}")

    def _parse_swagger_endpoints(self, swagger_data: Dict[str, Any]):
        """Parse Swagger/OpenAPI data to extract endpoint information."""
        try:
            paths = swagger_data.get("paths", {})

            for path, path_info in paths.items():
                for method, method_info in path_info.items():
                    if method.upper() in ["GET", "POST", "PUT", "DELETE", "PATCH"]:
                        # Generate endpoint key from path and method
                        endpoint_key = self._generate_endpoint_key(path, method, method_info)

                        # Extract parameters
                        parameters = []
                        if "parameters" in method_info:
                            for param in method_info["parameters"]:
                                parameters.append(param.get("name", ""))

                        # Extract request body parameters for POST/PUT
                        if method.upper() in ["POST", "PUT", "PATCH"] and "requestBody" in method_info:
                            request_body = method_info["requestBody"]
                            if "content" in request_body:
                                for content_type, content_info in request_body["content"].items():
                                    if "schema" in content_info and "properties" in content_info["schema"]:
                                        parameters.extend(content_info["schema"]["properties"].keys())

                        # Create endpoint configuration
                        endpoint_config = {
                            "path": path,
                            "method": method.upper(),
                            "description": method_info.get("summary", method_info.get("description", f"{method.upper()} {path}")),
                            "parameters": list(set(parameters)),  # Remove duplicates
                            "response_format": "API response data",
                            "source": "swagger"
                        }

                        # Only add if not already exists or if this is more detailed
                        if endpoint_key not in self.api_endpoints:
                            self.api_endpoints[endpoint_key] = endpoint_config
                            logger.info(f"Added Swagger endpoint: {endpoint_key}")

        except Exception as e:
            logger.error(f"Error parsing Swagger data: {e}")

    def _generate_endpoint_key(self, path: str, method: str, method_info: Dict[str, Any]) -> str:
        """Generate a meaningful endpoint key from path and method."""
        # Use operationId if available
        if "operationId" in method_info:
            return method_info["operationId"].lower()

        # Generate from path and method
        path_parts = [part for part in path.split("/") if part and not part.startswith("{")]
        key_parts = path_parts + [method.lower()]

        return "_".join(key_parts)
    
    def extract_parameters_from_text(self, user_request: str, endpoint_key: str) -> Dict[str, Any]:
        """Extract parameters from user request text based on endpoint requirements."""
        parameters = {}
        user_request_lower = user_request.lower()
        
        # Get expected parameters for this endpoint
        expected_params = self.api_endpoints.get(endpoint_key, {}).get("parameters", [])
        
        # Extract dates (multiple formats)
        date_patterns = [
            r'\b(\d{4}-\d{2}-\d{2})\b',  # YYYY-MM-DD
            r'\b(\d{1,2}/\d{1,2}/\d{4})\b',  # MM/DD/YYYY
            r'\b(\d{1,2}-\d{1,2}-\d{4})\b'   # MM-DD-YYYY
        ]
        
        dates_found = []
        for pattern in date_patterns:
            dates_found.extend(re.findall(pattern, user_request))
        
        # Assign dates based on context and expected parameters
        if "startDate" in expected_params and dates_found:
            if "from" in user_request_lower or "start" in user_request_lower:
                parameters["startDate"] = dates_found[0]
                if len(dates_found) > 1:
                    parameters["endDate"] = dates_found[1]
            else:
                parameters["startDate"] = dates_found[0]
        
        if "endDate" in expected_params and len(dates_found) > 1:
            parameters["endDate"] = dates_found[1]
        elif "endDate" in expected_params and len(dates_found) == 1:
            if "to" in user_request_lower or "end" in user_request_lower:
                parameters["endDate"] = dates_found[0]
        
        # Extract leave types
        if "leaveType" in expected_params:
            leave_types = ["casual", "sick", "annual", "vacation", "personal", "maternity", "paternity", "emergency"]
            for leave_type in leave_types:
                if leave_type in user_request_lower:
                    parameters["leaveType"] = leave_type
                    break
        
        # Extract attendance status
        if "status" in expected_params:
            status_keywords = {
                "present": ["present", "here", "in office", "working"],
                "absent": ["absent", "not here", "off", "away"],
                "late": ["late", "delayed"],
                "early": ["early", "leaving early"],
                "remote": ["remote", "work from home", "wfh"]
            }
            
            for status, keywords in status_keywords.items():
                if any(keyword in user_request_lower for keyword in keywords):
                    parameters["status"] = status
                    break
        
        # Extract reason
        if "reason" in expected_params:
            reason_patterns = [
                r'(?:because|for|reason|due to)\s+(.+?)(?:\.|$|,)',
                r'(?:as|since)\s+(.+?)(?:\.|$|,)'
            ]
            
            for pattern in reason_patterns:
                match = re.search(pattern, user_request, re.IGNORECASE)
                if match:
                    parameters["reason"] = match.group(1).strip()
                    break
        
        # Extract location
        if "location" in expected_params:
            location_patterns = [
                r'(?:at|from|in)\s+([A-Za-z\s]+?)(?:\.|$|,)',
                r'location[:\s]+([A-Za-z\s]+?)(?:\.|$|,)'
            ]
            
            for pattern in location_patterns:
                match = re.search(pattern, user_request, re.IGNORECASE)
                if match:
                    parameters["location"] = match.group(1).strip()
                    break
        
        # Extract half day indicator
        if "halfDay" in expected_params:
            half_day_keywords = ["half day", "half-day", "morning only", "afternoon only"]
            parameters["halfDay"] = any(keyword in user_request_lower for keyword in half_day_keywords)
        
        # Add current timestamp if needed
        if "timestamp" in expected_params and "timestamp" not in parameters:
            parameters["timestamp"] = datetime.now().isoformat()
        
        return parameters
    
    def find_best_endpoint(self, user_request: str) -> Optional[str]:
        """Find the best matching endpoint based on user request using intelligent analysis."""
        return self._intelligent_endpoint_selection(user_request)

    def _intelligent_endpoint_selection(self, user_request: str) -> Optional[str]:
        """Use AI-powered analysis to select the best endpoint for the user request."""
        try:
            # Create a detailed prompt for endpoint selection
            endpoint_descriptions = []
            for endpoint_key, endpoint_info in self.api_endpoints.items():
                endpoint_descriptions.append(
                    f"- {endpoint_key}: {endpoint_info['description']} "
                    f"(Method: {endpoint_info['method']}, "
                    f"Parameters: {', '.join(endpoint_info.get('parameters', []))})"
                )

            # Try to use Google ADK for intelligent selection if available
            try:
                import google_adk
                from google_adk.google.adk.models.google_llm import GoogleLLM

                # Create a prompt for endpoint selection
                selection_prompt = f"""
You are an API endpoint selector for a WagonHR system. Given a user request, select the most appropriate API endpoint.

Available API endpoints:
{chr(10).join(endpoint_descriptions)}

User Request: "{user_request}"

Instructions:
1. Analyze the user's intent and requirements
2. Match the request to the most appropriate endpoint
3. Consider the HTTP method and required parameters
4. Return ONLY the endpoint key (e.g., "leave_balance", "leave_request", etc.)
5. If no endpoint matches well, return "NONE"

Response (endpoint key only):"""

                # Initialize LLM
                llm = GoogleLLM(model_name="gemini-2.0-flash")

                # Get response
                response = llm.generate_content(selection_prompt)

                if response and hasattr(response, 'text'):
                    selected_endpoint = response.text.strip().lower()

                    # Validate the selected endpoint
                    if selected_endpoint in self.api_endpoints:
                        logger.info(f"AI selected endpoint: {selected_endpoint}")
                        return selected_endpoint
                    elif selected_endpoint == "none":
                        logger.info("AI determined no suitable endpoint")
                        return None

            except ImportError:
                logger.info("Google ADK not available, falling back to rule-based selection")
            except Exception as e:
                logger.warning(f"AI endpoint selection failed: {e}, falling back to rule-based selection")

            # Fallback to enhanced rule-based selection
            return self._rule_based_endpoint_selection(user_request)

        except Exception as e:
            logger.error(f"Error in intelligent endpoint selection: {e}")
            return self._rule_based_endpoint_selection(user_request)

    def _rule_based_endpoint_selection(self, user_request: str) -> Optional[str]:
        """Enhanced rule-based endpoint selection as fallback."""
        user_request_lower = user_request.lower()

        # Analyze intent patterns instead of hardcoded keywords
        intent_patterns = {
            "leave_balance": {
                "patterns": [
                    r"\b(balance|remaining|left|available|how many)\b.*\b(leave|days?)\b",
                    r"\b(leave|vacation|sick)\b.*\b(balance|remaining|left|available)\b",
                    r"\bcheck.*\b(leave|vacation)\b.*\b(balance|days?)\b"
                ],
                "weight": 1.0
            },
            "leave_request": {
                "patterns": [
                    r"\b(apply|request|submit|take|book)\b.*\b(leave|vacation|time off)\b",
                    r"\b(leave|vacation)\b.*\b(apply|request|submit|from|to)\b",
                    r"\bneed.*\b(leave|time off|vacation)\b"
                ],
                "weight": 1.0
            },
            "leave_history": {
                "patterns": [
                    r"\b(history|past|previous|taken|used)\b.*\b(leave|vacation)\b",
                    r"\b(leave|vacation)\b.*\b(history|past|previous|taken)\b",
                    r"\bshow.*\b(leave|vacation)\b.*\b(history|record)\b"
                ],
                "weight": 1.0
            },
            "attendance_record": {
                "patterns": [
                    r"\b(record|mark|check in|check out|present|absent)\b.*\battendance\b",
                    r"\battendance\b.*\b(record|mark|check|present|absent)\b",
                    r"\b(mark|record)\b.*\b(present|absent|attendance)\b"
                ],
                "weight": 1.0
            },
            "attendance_report": {
                "patterns": [
                    r"\b(report|summary|timesheet)\b.*\battendance\b",
                    r"\battendance\b.*\b(report|summary|timesheet)\b",
                    r"\bshow.*\battendance\b.*\b(report|summary)\b"
                ],
                "weight": 1.0
            },
            "employee_profile": {
                "patterns": [
                    r"\b(profile|information|details|about me)\b",
                    r"\bmy.*\b(profile|information|details)\b",
                    r"\bemployee.*\b(profile|information|details)\b"
                ],
                "weight": 1.0
            }
        }

        # Score endpoints based on pattern matches
        scores = {}
        for endpoint_key, config in intent_patterns.items():
            score = 0
            for pattern in config["patterns"]:
                if re.search(pattern, user_request_lower):
                    score += config["weight"]
            scores[endpoint_key] = score

        # Return the highest scoring endpoint
        if scores and max(scores.values()) > 0:
            best_endpoint = max(scores, key=scores.get)
            logger.info(f"Rule-based selection: {best_endpoint} (score: {scores[best_endpoint]})")
            return best_endpoint

        logger.info("No suitable endpoint found")
        return None
    
    def make_api_call(self, endpoint_key: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Make API call to WagonHR with dynamic request construction."""
        try:
            # Get endpoint details
            endpoint_info = self.api_endpoints.get(endpoint_key)
            if not endpoint_info:
                return {
                    "status": "error",
                    "message": f"Unknown endpoint: {endpoint_key}"
                }
            
            # Authenticate with WagonHR
            if not hr_auth.authenticate_wagonhr():
                return {
                    "status": "error",
                    "message": "Failed to authenticate with WagonHR"
                }
            
            # Prepare headers
            headers = {
                "Authorization": f"Bearer {hr_auth.wagonhr_access_token}",
                "Content-Type": "application/json",
                "Accept": "application/json",
                "x-tenantid": "mouritech",
                "x-entityid": "US-MT-002"
            }
            
            # Construct URL
            url = f"{self.base_url}{endpoint_info['path']}"
            method = endpoint_info['method']
            
            logger.info(f"Making {method} request to: {url}")
            logger.info(f"Parameters: {parameters}")
            
            # Make the API call
            if method == "GET":
                response = requests.get(url, headers=headers, params=parameters, timeout=30)
            elif method == "POST":
                response = requests.post(url, headers=headers, json=parameters, timeout=30)
            elif method == "PUT":
                response = requests.put(url, headers=headers, json=parameters, timeout=30)
            elif method == "DELETE":
                response = requests.delete(url, headers=headers, params=parameters, timeout=30)
            else:
                return {
                    "status": "error",
                    "message": f"Unsupported HTTP method: {method}"
                }
            
            logger.info(f"Response status: {response.status_code}")
            
            # Process response
            if response.status_code == 200:
                try:
                    response_data = response.json()
                    return {
                        "status": "success",
                        "data": response_data,
                        "endpoint": endpoint_key,
                        "message": f"Successfully called {endpoint_info['description']}"
                    }
                except json.JSONDecodeError:
                    return {
                        "status": "success",
                        "data": {"text": response.text},
                        "endpoint": endpoint_key,
                        "message": f"Successfully called {endpoint_info['description']}"
                    }
            else:
                return {
                    "status": "error",
                    "message": f"API call failed with status {response.status_code}",
                    "details": response.text,
                    "endpoint": endpoint_key
                }
                
        except Exception as e:
            logger.error(f"Error making API call: {e}")
            return {
                "status": "error",
                "message": f"Error making API call: {str(e)}",
                "endpoint": endpoint_key
            }

# Initialize the flexible agent
flexible_wagonhr_agent = FlexibleWagonHRAgent()

def add_custom_endpoint(endpoint_key: str, endpoint_config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Add a custom API endpoint configuration to the flexible agent.

    Args:
        endpoint_key: Unique key for the endpoint
        endpoint_config: Configuration dictionary with path, method, description, parameters

    Returns:
        Dictionary containing the operation status
    """
    try:
        required_fields = ["path", "method", "description"]
        for field in required_fields:
            if field not in endpoint_config:
                return {
                    "status": "error",
                    "message": f"Missing required field: {field}"
                }

        # Add default values for optional fields
        endpoint_config.setdefault("parameters", [])
        endpoint_config.setdefault("response_format", "API response data")

        # Add to the agent's endpoints
        flexible_wagonhr_agent.api_endpoints[endpoint_key] = endpoint_config

        logger.info(f"Added custom endpoint: {endpoint_key}")
        return {
            "status": "success",
            "message": f"Successfully added custom endpoint: {endpoint_key}",
            "endpoint": endpoint_config
        }

    except Exception as e:
        logger.error(f"Error adding custom endpoint: {e}")
        return {
            "status": "error",
            "message": f"Error adding custom endpoint: {str(e)}"
        }

def call_custom_api(endpoint_details: Dict[str, Any], user_request: str, tool_context: Optional[object] = None) -> Dict[str, Any]:
    """
    Call a custom WagonHR API endpoint with details provided on-the-fly.

    Args:
        endpoint_details: Dictionary containing API endpoint details
        user_request: Natural language request from the user
        tool_context: Optional tool context for session management

    Returns:
        Dictionary containing the response from WagonHR API
    """
    try:
        logger.info(f"=== CUSTOM WAGONHR API CALL ===")
        logger.info(f"Endpoint Details: {endpoint_details}")
        logger.info(f"User Request: {user_request}")

        # Validate endpoint details
        required_fields = ["path", "method"]
        for field in required_fields:
            if field not in endpoint_details:
                return {
                    "status": "error",
                    "message": f"Missing required endpoint field: {field}"
                }

        # Extract parameters from user request
        expected_params = endpoint_details.get("parameters", [])
        parameters = {}

        # Use the existing parameter extraction logic
        temp_endpoint_key = "custom_temp"
        flexible_wagonhr_agent.api_endpoints[temp_endpoint_key] = endpoint_details
        parameters = flexible_wagonhr_agent.extract_parameters_from_text(user_request, temp_endpoint_key)

        # Clean up temporary endpoint
        del flexible_wagonhr_agent.api_endpoints[temp_endpoint_key]

        logger.info(f"Extracted parameters: {parameters}")

        # Authenticate with WagonHR
        if not hr_auth.authenticate_wagonhr():
            return {
                "status": "error",
                "message": "Failed to authenticate with WagonHR"
            }

        # Prepare headers
        headers = {
            "Authorization": f"Bearer {hr_auth.wagonhr_access_token}",
            "Content-Type": "application/json",
            "Accept": "application/json",
            "x-tenantid": "mouritech",
            "x-entityid": "US-MT-002"
        }

        # Add any custom headers from endpoint details
        if "headers" in endpoint_details:
            headers.update(endpoint_details["headers"])

        # Construct URL
        base_url = endpoint_details.get("base_url", "https://dev-api-wagonhr.mouritech.net")
        url = f"{base_url}{endpoint_details['path']}"
        method = endpoint_details['method'].upper()

        logger.info(f"Making {method} request to: {url}")

        # Make the API call
        if method == "GET":
            response = requests.get(url, headers=headers, params=parameters, timeout=30)
        elif method == "POST":
            response = requests.post(url, headers=headers, json=parameters, timeout=30)
        elif method == "PUT":
            response = requests.put(url, headers=headers, json=parameters, timeout=30)
        elif method == "DELETE":
            response = requests.delete(url, headers=headers, params=parameters, timeout=30)
        elif method == "PATCH":
            response = requests.patch(url, headers=headers, json=parameters, timeout=30)
        else:
            return {
                "status": "error",
                "message": f"Unsupported HTTP method: {method}"
            }

        logger.info(f"Response status: {response.status_code}")

        # Process response
        if response.status_code in [200, 201, 202, 204]:
            try:
                if response.text:
                    response_data = response.json()
                else:
                    response_data = {"message": "Success - No content returned"}

                result = {
                    "status": "success",
                    "data": response_data,
                    "endpoint_details": endpoint_details,
                    "message": f"Successfully called {endpoint_details.get('description', 'custom API')}"
                }

                # Store in session if context provided
                if tool_context:
                    tool_context.state["last_custom_api_call"] = {
                        "user_request": user_request,
                        "endpoint_details": endpoint_details,
                        "parameters": parameters,
                        "result": result,
                        "timestamp": datetime.now().isoformat()
                    }
                    logger.info("Custom API call results stored in session state")

                return result

            except json.JSONDecodeError:
                return {
                    "status": "success",
                    "data": {"text": response.text},
                    "endpoint_details": endpoint_details,
                    "message": f"Successfully called {endpoint_details.get('description', 'custom API')}"
                }
        else:
            return {
                "status": "error",
                "message": f"API call failed with status {response.status_code}",
                "details": response.text,
                "endpoint_details": endpoint_details
            }

    except Exception as e:
        logger.error(f"Error making custom API call: {e}")
        return {
            "status": "error",
            "message": f"Error making custom API call: {str(e)}"
        }

def process_wagonhr_request(user_request: str, tool_context: Optional[object] = None) -> Dict[str, Any]:
    """
    Process a user request using the flexible WagonHR agent.
    
    Args:
        user_request: Natural language request from the user
        tool_context: Optional tool context for session management
        
    Returns:
        Dictionary containing the response from WagonHR API
    """
    try:
        logger.info(f"=== FLEXIBLE WAGONHR REQUEST ===")
        logger.info(f"User Request: {user_request}")
        
        # Find the best matching endpoint
        endpoint_key = flexible_wagonhr_agent.find_best_endpoint(user_request)
        if not endpoint_key:
            return {
                "status": "error",
                "message": "Could not determine the appropriate API endpoint for your request",
                "available_endpoints": list(flexible_wagonhr_agent.api_endpoints.keys()),
                "suggestion": "Please try rephrasing your request or be more specific"
            }
        
        logger.info(f"Selected endpoint: {endpoint_key}")
        
        # Extract parameters from the user request
        parameters = flexible_wagonhr_agent.extract_parameters_from_text(user_request, endpoint_key)
        logger.info(f"Extracted parameters: {parameters}")
        
        # Make the API call
        result = flexible_wagonhr_agent.make_api_call(endpoint_key, parameters)
        
        # Store in session if context provided
        if tool_context and result.get("status") == "success":
            tool_context.state["last_wagonhr_request"] = {
                "user_request": user_request,
                "endpoint": endpoint_key,
                "parameters": parameters,
                "result": result,
                "timestamp": datetime.now().isoformat()
            }
            logger.info("WagonHR request results stored in session state")
        
        logger.info(f"Flexible WagonHR request completed: {result.get('status')}")
        return result
        
    except Exception as e:
        logger.error(f"Error in flexible WagonHR request: {e}")
        return {
            "status": "error",
            "message": f"Error processing request: {str(e)}"
        }
