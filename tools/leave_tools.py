"""
Unified Leave Management Tools Module.

This module provides comprehensive leave management functionality using the
Flexible WagonHR Agent for real-time API integration. All leave-related
operations are consolidated here for better organization and maintainability.

Replaces both hr_api.py leave functions and old leave_tools.py mock functions
with a single, unified module that uses real WagonHR APIs.
"""

import logging
import datetime
from typing import Dict, Any, Optional

from google.adk.tools.tool_context import ToolContext

# Set up logger
logger = logging.getLogger(__name__)

# Leave types and their codes
LEAVE_TYPES = {
    "casual": "CL",
    "sick": "SL", 
    "annual": "AL",
    "vacation": "VL",
    "compensatory": "COMP",
    "maternity": "ML",
    "paternity": "PL",
    "bereavement": "BL",
    "emergency": "EL",
    "personal": "PL",
    "unpaid": "UL"
}

def get_leave_balance(
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Retrieves leave balance for the authenticated employee using WagonHR API.

    This tool directly calls the WagonHR leave balance endpoint.
    The API automatically identifies the employee using the auth token.

    Args:
        tool_context: Tool context for accessing session state

    Returns:
        Dictionary containing operation status and leave balance details
    """
    try:
        logger.info("=== GET LEAVE BALANCE ===")
        logger.info("Calling WagonHR leave balance endpoint directly")

        # Import required modules
        import requests
        from utils.hr_auth import hr_auth

        # Prepare the API call
        base_url = "https://dev-api-wagonhr.mouritech.net"
        endpoint = "/api/hrms/leave-attendance/employee-leaves"
        url = f"{base_url}{endpoint}"

        # Get authentication headers from hr_auth manager
        auth_headers = hr_auth.get_wagonhr_auth_headers()
        if not auth_headers:
            logger.error("Failed to get WagonHR authentication headers")
            return {
                "status": "error",
                "message": "Authentication failed - unable to get WagonHR token"
            }

        # Prepare headers with authentication and tenant context
        headers = {
            **auth_headers,
            "Accept": "application/json",
            "x-tenantid": "mouritech",
            "x-entityid": "US-MT-002"
        }

        logger.info(f"Making GET request to: {url}")

        # Make the API call
        response = requests.get(url, headers=headers, timeout=30)

        logger.info(f"Response status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            logger.info("Leave balance retrieved successfully")

            # Handle different response formats from WagonHR API
            if isinstance(data, dict) and "leave_balance" in data:
                leave_balance = data["leave_balance"]
            elif isinstance(data, list):
                # Convert list format to dict format
                leave_balance = {}
                for item in data:
                    if isinstance(item, dict):
                        leave_type = item.get("leaveType", "unknown").lower()
                        balance = item.get("balance", 0)
                        leave_balance[leave_type] = balance
            elif isinstance(data, dict):
                leave_balance = data
            else:
                # Provide default leave types with 0 values when API returns unexpected format
                logger.info("API returned unexpected format - providing default leave types with 0 values")
                leave_balance = {
                    "casual": 0,
                    "sick": 0,
                    "annual": 0,
                    "vacation": 0,
                    "personal": 0,
                    "maternity": 0,
                    "paternity": 0,
                    "bereavement": 0,
                    "emergency": 0
                }

            return {
                "status": "success",
                "message": "Leave balance retrieved successfully",
                "leave_balance": leave_balance
            }
        else:
            error_msg = f"API call failed with status {response.status_code}"
            try:
                error_data = response.json()
                error_msg += f": {error_data.get('message', 'Unknown error')}"
            except:
                error_msg += f": {response.text}"

            logger.error(error_msg)
            return {
                "status": "error",
                "message": error_msg
            }

    except Exception as e:
        logger.error(f"Error retrieving leave balance: {e}")
        return {
            "status": "error",
            "message": f"Error retrieving leave balance: {str(e)}"
        }

def request_leave(
    employee_id: Optional[str] = None,
    email: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    leave_type: Optional[str] = None,
    reason: Optional[str] = None,
    half_day: Optional[bool] = False,
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Creates a new leave request using WagonHR API.

    This tool directly calls the WagonHR leave request endpoint.
    The API automatically identifies the employee using the auth token.

    Args:
        employee_id: Optional employee ID (for compatibility)
        email: Optional employee email (for compatibility)
        start_date: Start date of the leave (YYYY-MM-DD)
        end_date: End date of the leave (YYYY-MM-DD)
        leave_type: Type of leave (casual, sick, annual, etc.)
        reason: Reason for the leave
        half_day: Whether this is a half-day leave
        tool_context: Tool context for accessing session state

    Returns:
        Dictionary containing operation status and leave request details
    """
    try:
        logger.info("=== REQUEST LEAVE ===")
        logger.info("Calling WagonHR leave request endpoint directly")

        # Validate required parameters
        if not start_date or not end_date or not leave_type or not reason:
            return {
                "status": "error",
                "message": "start_date, end_date, leave_type, and reason are required"
            }

        # Validate leave type
        if leave_type.lower() not in LEAVE_TYPES:
            return {
                "status": "error",
                "message": f"Invalid leave type: {leave_type}. Valid types are: {', '.join(LEAVE_TYPES.keys())}"
            }

        # Import required modules
        import requests
        from utils import hr_auth

        # Prepare the API call
        base_url = "https://dev-api-wagonhr.mouritech.net"
        endpoint = "/api/hrms/leave-attendance/employee-leaves/request"
        url = f"{base_url}{endpoint}"

        # Get authentication headers from hr_auth manager
        auth_headers = hr_auth.get_wagonhr_auth_headers()
        if not auth_headers:
            logger.error("Failed to get WagonHR authentication headers")
            return {
                "status": "error",
                "message": "Authentication failed - unable to get WagonHR token"
            }

        # Prepare headers with authentication and tenant context
        headers = {
            **auth_headers,
            "Accept": "application/json",
            "x-tenantid": "mouritech",
            "x-entityid": "US-MT-002"
        }

        # Prepare request payload according to WagonHR API structure
        from datetime import datetime
        current_time = datetime.now().isoformat() + "Z"

        payload = {
            "createdDate": current_time,
            "updatedDate": current_time,
            "createdBy": "hr-ai-assistant",
            "updatedBy": "hr-ai-assistant",
            "startDate": f"{start_date}T00:00:00.000Z",
            "endDate": f"{end_date}T23:59:59.999Z",
            "leaveTypeName": leave_type.lower(),
            "note": reason,
            "firstDayPreference": "full_day" if not half_day else "half_day",
            "lastDayPreference": "full_day" if not half_day else "half_day",
            "isActive": True,
            "leaveCount": 1,
            "notifyTo": []
        }

        logger.info(f"Making POST request to: {url}")
        logger.info(f"Payload: {payload}")

        # Make the API call
        response = requests.post(url, json=payload, headers=headers, timeout=30)

        logger.info(f"Response status: {response.status_code}")

        if response.status_code in [200, 201]:
            data = response.json()
            logger.info("Leave request submitted successfully")

            return {
                "status": "success",
                "message": "Leave request submitted successfully",
                "leave_request": data
            }
        else:
            error_msg = f"API call failed with status {response.status_code}"
            try:
                error_data = response.json()
                error_msg += f": {error_data.get('message', 'Unknown error')}"
            except:
                error_msg += f": {response.text}"

            logger.error(error_msg)
            return {
                "status": "error",
                "message": error_msg
            }

    except Exception as e:
        logger.error(f"Error submitting leave request: {e}")
        return {
            "status": "error",
            "message": f"Error submitting leave request: {str(e)}"
        }

def get_leave_history(
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    status: Optional[str] = None,
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Retrieves leave history for the authenticated employee using WagonHR API.

    This tool directly calls the WagonHR leave history endpoint.

    Args:
        start_date: Optional start date filter (YYYY-MM-DD)
        end_date: Optional end date filter (YYYY-MM-DD)
        status: Optional status filter (Pending, Approved, Rejected, Cancelled)
        tool_context: Tool context for accessing session state

    Returns:
        Dictionary containing operation status and leave history details
    """
    try:
        logger.info("=== GET LEAVE HISTORY ===")
        logger.info("Calling WagonHR leave history endpoint directly")

        # Import required modules
        import requests
        from utils.hr_auth import hr_auth

        # Prepare the API call
        base_url = "https://dev-api-wagonhr.mouritech.net"
        endpoint = "/api/hrms/leave-attendance/employee-leavey-history"
        url = f"{base_url}{endpoint}"

        # Get authentication headers from hr_auth manager
        auth_headers = hr_auth.get_wagonhr_auth_headers()
        if not auth_headers:
            logger.error("Failed to get WagonHR authentication headers")
            return {
                "status": "error",
                "message": "Authentication failed - unable to get WagonHR token"
            }

        # Prepare headers with authentication and tenant context
        headers = {
            **auth_headers,
            "Accept": "application/json",
            "x-tenantid": "mouritech",
            "x-entityid": "US-MT-002"
        }

        # Prepare query parameters
        params = {}
        if start_date:
            params["startDate"] = start_date
        if end_date:
            params["endDate"] = end_date
        if status:
            params["status"] = status

        logger.info(f"Making GET request to: {url}")
        if params:
            logger.info(f"Query parameters: {params}")

        # Make the API call
        response = requests.get(url, headers=headers, params=params, timeout=30)

        logger.info(f"Response status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            logger.info("Leave history retrieved successfully")

            return {
                "status": "success",
                "message": "Leave history retrieved successfully",
                "leave_history": data
            }
        else:
            error_msg = f"API call failed with status {response.status_code}"
            try:
                error_data = response.json()
                error_msg += f": {error_data.get('message', 'Unknown error')}"
            except:
                error_msg += f": {response.text}"

            logger.error(error_msg)
            return {
                "status": "error",
                "message": error_msg
            }

    except Exception as e:
        logger.error(f"Error retrieving leave history: {e}")
        return {
            "status": "error",
            "message": f"Error retrieving leave history: {str(e)}"
        }

def approve_leave(
    leave_id: str,
    notes: Optional[str] = None,
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Approves a leave request using WagonHR API.

    This tool directly calls the WagonHR leave approval endpoint.

    Args:
        leave_id: ID of the leave request to approve
        notes: Optional notes from the approver
        tool_context: Tool context for accessing session state

    Returns:
        Dictionary containing operation status and updated leave request details
    """
    try:
        logger.info("=== APPROVE LEAVE ===")
        logger.info(f"Calling WagonHR leave approval endpoint for request: {leave_id}")

        # Import required modules
        import requests
        from utils import hr_auth

        # Prepare the API call
        base_url = "https://dev-api-wagonhr.mouritech.net"
        endpoint = "/api/hrms/leave/approve"
        url = f"{base_url}{endpoint}"

        # Get authentication headers from hr_auth manager
        auth_headers = hr_auth.get_wagonhr_auth_headers()
        if not auth_headers:
            logger.error("Failed to get WagonHR authentication headers")
            return {
                "status": "error",
                "message": "Authentication failed - unable to get WagonHR token"
            }

        # Prepare headers with authentication and tenant context
        headers = {
            **auth_headers,
            "Accept": "application/json",
            "x-tenantid": "mouritech",
            "x-entityid": "US-MT-002"
        }

        # Prepare request payload
        payload = {
            "requestId": leave_id,
            "status": "approved"
        }

        if notes:
            payload["comments"] = notes

        logger.info(f"Making PUT request to: {url}")
        logger.info(f"Payload: {payload}")

        # Make the API call
        response = requests.put(url, json=payload, headers=headers, timeout=30)

        logger.info(f"Response status: {response.status_code}")

        if response.status_code in [200, 201]:
            data = response.json()
            logger.info("Leave request approved successfully")

            return {
                "status": "success",
                "message": f"Leave request {leave_id} approved successfully",
                "leave_request": data
            }
        else:
            error_msg = f"API call failed with status {response.status_code}"
            try:
                error_data = response.json()
                error_msg += f": {error_data.get('message', 'Unknown error')}"
            except:
                error_msg += f": {response.text}"

            logger.error(error_msg)
            return {
                "status": "error",
                "message": error_msg
            }

    except Exception as e:
        logger.error(f"Error approving leave request: {e}")
        return {
            "status": "error",
            "message": f"Error approving leave request: {str(e)}"
        }

def reject_leave(
    leave_id: str,
    reason: Optional[str] = None,
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Rejects a leave request using WagonHR API.

    This tool directly calls the WagonHR leave rejection endpoint.

    Args:
        leave_id: ID of the leave request to reject
        reason: Reason for rejection
        tool_context: Tool context for accessing session state

    Returns:
        Dictionary containing operation status and updated leave request details
    """
    try:
        logger.info("=== REJECT LEAVE ===")
        logger.info(f"Calling WagonHR leave rejection endpoint for request: {leave_id}")

        # Import required modules
        import requests
        from utils import hr_auth

        # Prepare the API call
        base_url = "https://dev-api-wagonhr.mouritech.net"
        endpoint = "/api/hrms/leave/reject"
        url = f"{base_url}{endpoint}"

        # Get authentication headers from hr_auth manager
        auth_headers = hr_auth.get_wagonhr_auth_headers()
        if not auth_headers:
            logger.error("Failed to get WagonHR authentication headers")
            return {
                "status": "error",
                "message": "Authentication failed - unable to get WagonHR token"
            }

        # Prepare headers with authentication and tenant context
        headers = {
            **auth_headers,
            "Accept": "application/json",
            "x-tenantid": "mouritech",
            "x-entityid": "US-MT-002"
        }

        # Prepare request payload
        payload = {
            "requestId": leave_id,
            "status": "rejected"
        }

        if reason:
            payload["reason"] = reason

        logger.info(f"Making PUT request to: {url}")
        logger.info(f"Payload: {payload}")

        # Make the API call
        response = requests.put(url, json=payload, headers=headers, timeout=30)

        logger.info(f"Response status: {response.status_code}")

        if response.status_code in [200, 201]:
            data = response.json()
            logger.info("Leave request rejected successfully")

            return {
                "status": "success",
                "message": f"Leave request {leave_id} rejected successfully",
                "leave_request": data
            }
        else:
            error_msg = f"API call failed with status {response.status_code}"
            try:
                error_data = response.json()
                error_msg += f": {error_data.get('message', 'Unknown error')}"
            except:
                error_msg += f": {response.text}"

            logger.error(error_msg)
            return {
                "status": "error",
                "message": error_msg
            }

    except Exception as e:
        logger.error(f"Error rejecting leave request: {e}")
        return {
            "status": "error",
            "message": f"Error rejecting leave request: {str(e)}"
        }

def cancel_leave(
    leave_id: str,
    reason: Optional[str] = None,
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Cancels a leave request using WagonHR API.

    This tool directly calls the WagonHR leave cancellation endpoint.

    Args:
        leave_id: ID of the leave request to cancel
        reason: Reason for cancellation
        tool_context: Tool context for accessing session state

    Returns:
        Dictionary containing operation status and updated leave request details
    """
    try:
        logger.info("=== CANCEL LEAVE ===")
        logger.info(f"Calling WagonHR leave cancellation endpoint for request: {leave_id}")

        # Import required modules
        import requests
        from utils import hr_auth

        # Prepare the API call
        base_url = "https://dev-api-wagonhr.mouritech.net"
        endpoint = f"/api/hrms/leave-attendance/employee-leave-application/cancel-leave?leaveApplicationId={leave_id}"
        url = f"{base_url}{endpoint}"

        # Get authentication headers from hr_auth manager
        auth_headers = hr_auth.get_wagonhr_auth_headers()
        if not auth_headers:
            logger.error("Failed to get WagonHR authentication headers")
            return {
                "status": "error",
                "message": "Authentication failed - unable to get WagonHR token"
            }

        # Prepare headers with authentication and tenant context
        headers = {
            **auth_headers,
            "Accept": "application/json",
            "x-tenantid": "mouritech",
            "x-entityid": "US-MT-002"
        }

        logger.info(f"Making DELETE request to: {url}")
        if reason:
            logger.info(f"Cancellation reason: {reason}")

        # Make the API call (no payload needed as leaveApplicationId is in query parameter)
        response = requests.delete(url, headers=headers, timeout=30)

        logger.info(f"Response status: {response.status_code}")

        if response.status_code in [200, 201, 204]:
            try:
                data = response.json() if response.content else {}
            except:
                data = {}
            logger.info("Leave request cancelled successfully")

            return {
                "status": "success",
                "message": f"Leave request {leave_id} cancelled successfully",
                "leave_request": data
            }
        else:
            error_msg = f"API call failed with status {response.status_code}"
            try:
                error_data = response.json()
                error_msg += f": {error_data.get('message', 'Unknown error')}"
            except:
                error_msg += f": {response.text}"

            logger.error(error_msg)
            return {
                "status": "error",
                "message": error_msg
            }

    except Exception as e:
        logger.error(f"Error cancelling leave request: {e}")
        return {
            "status": "error",
            "message": f"Error cancelling leave request: {str(e)}"
        }

def modify_leave(
    leave_id: str,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    leave_type: Optional[str] = None,
    reason: Optional[str] = None,
    half_day: Optional[bool] = None,
    modification_reason: Optional[str] = "Schedule change",
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Modifies an existing leave request using WagonHR API.

    This tool directly calls the WagonHR leave modification endpoint.

    Args:
        leave_id: ID of the leave request to modify
        start_date: New start date (YYYY-MM-DD)
        end_date: New end date (YYYY-MM-DD)
        leave_type: New leave type
        reason: New reason for the leave
        half_day: New half-day flag
        modification_reason: Reason for the modification
        tool_context: Tool context for accessing session state

    Returns:
        Dictionary containing operation status and updated leave request details
    """
    try:
        logger.info("=== MODIFY LEAVE ===")
        logger.info(f"Calling WagonHR leave modification endpoint for request: {leave_id}")

        # Import required modules
        import requests
        from utils import hr_auth

        # Prepare the API call
        base_url = "https://dev-api-wagonhr.mouritech.net"
        endpoint = "/api/hrms/leave/modify"
        url = f"{base_url}{endpoint}"

        # Get authentication headers from hr_auth manager
        auth_headers = hr_auth.get_wagonhr_auth_headers()
        if not auth_headers:
            logger.error("Failed to get WagonHR authentication headers")
            return {
                "status": "error",
                "message": "Authentication failed - unable to get WagonHR token"
            }

        # Prepare headers with authentication and tenant context
        headers = {
            **auth_headers,
            "Accept": "application/json",
            "x-tenantid": "mouritech",
            "x-entityid": "US-MT-002"
        }

        # Prepare request payload
        payload = {
            "requestId": leave_id
        }

        # Add optional fields only if provided
        if start_date:
            payload["startDate"] = start_date
        if end_date:
            payload["endDate"] = end_date
        if leave_type:
            payload["leaveType"] = leave_type.lower()
        if reason:
            payload["reason"] = reason
        if half_day is not None:
            payload["halfDay"] = half_day
        if modification_reason:
            payload["modificationReason"] = modification_reason

        logger.info(f"Making PUT request to: {url}")
        logger.info(f"Payload: {payload}")

        # Make the API call
        response = requests.put(url, json=payload, headers=headers, timeout=30)

        logger.info(f"Response status: {response.status_code}")

        if response.status_code in [200, 201]:
            data = response.json()
            logger.info("Leave request modified successfully")

            return {
                "status": "success",
                "message": f"Leave request {leave_id} modified successfully",
                "leave_request": data
            }
        else:
            error_msg = f"API call failed with status {response.status_code}"
            try:
                error_data = response.json()
                error_msg += f": {error_data.get('message', 'Unknown error')}"
            except:
                error_msg += f": {response.text}"

            logger.error(error_msg)
            return {
                "status": "error",
                "message": error_msg
            }

    except Exception as e:
        logger.error(f"Error modifying leave request: {e}")
        return {
            "status": "error",
            "message": f"Error modifying leave request: {str(e)}"
        }

def get_pending_leaves(
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Retrieves all pending leave requests for the authenticated employee using WagonHR API.

    This tool directly calls the WagonHR pending leaves endpoint.

    Args:
        tool_context: Tool context for accessing session state

    Returns:
        Dictionary containing operation status and pending leave requests
    """
    try:
        logger.info("=== GET PENDING LEAVES ===")
        logger.info("Calling WagonHR pending leaves endpoint directly")

        # Import required modules
        import requests
        from utils.hr_auth import hr_auth

        # Prepare the API call
        base_url = "https://dev-api-wagonhr.mouritech.net"
        endpoint = "/api/hrms/leave-attendance/employee-leave-application/pending-leaves"
        url = f"{base_url}{endpoint}"

        # Get authentication headers from hr_auth manager
        auth_headers = hr_auth.get_wagonhr_auth_headers()
        if not auth_headers:
            logger.error("Failed to get WagonHR authentication headers")
            return {
                "status": "error",
                "message": "Authentication failed - unable to get WagonHR token"
            }

        # Prepare headers with authentication and tenant context
        headers = {
            **auth_headers,
            "Accept": "application/json",
            "x-tenantid": "mouritech",
            "x-entityid": "US-MT-002"
        }

        logger.info(f"Making GET request to: {url}")

        # Make the API call
        response = requests.get(url, headers=headers, timeout=30)

        logger.info(f"Response status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            logger.info("Pending leaves retrieved successfully")

            return {
                "status": "success",
                "message": f"Found {len(data) if isinstance(data, list) else 'unknown number of'} pending leave requests",
                "pending_leaves": data
            }
        else:
            error_msg = f"API call failed with status {response.status_code}"
            try:
                error_data = response.json()
                error_msg += f": {error_data.get('message', 'Unknown error')}"
            except:
                error_msg += f": {response.text}"

            logger.error(error_msg)
            return {
                "status": "error",
                "message": error_msg
            }

    except Exception as e:
        logger.error(f"Error retrieving pending leaves: {e}")
        return {
            "status": "error",
            "message": f"Error retrieving pending leaves: {str(e)}"
        }

def request_leave_intelligent(
    leave_request_text: str,
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Creates a leave request using LLM-powered intelligent parsing of natural language.

    This tool uses AI to understand natural language requests like:
    - "I need sick leave for next Tuesday"
    - "Apply annual leave from tomorrow to Friday"
    - "I want to take a day off on coming Monday for personal reasons"

    Args:
        leave_request_text: Natural language leave request
        tool_context: Tool context for accessing session state

    Returns:
        Dictionary containing operation status and leave request details
    """
    try:
        logger.info("=== LLM-POWERED INTELLIGENT LEAVE REQUEST ===")
        logger.info(f"Processing natural language request: '{leave_request_text}'")

        # Use LLM to intelligently parse the leave request
        parsed_info = _llm_parse_leave_request(leave_request_text, tool_context)

        if not parsed_info.get("success"):
            return {
                "status": "error",
                "message": parsed_info.get("error", "Could not understand the leave request"),
                "suggestion": "Please specify the date, leave type, and reason. Example: 'I need sick leave for next Tuesday because I'm not feeling well'"
            }

        # Extract the parsed information
        start_date = parsed_info.get("start_date")
        end_date = parsed_info.get("end_date", start_date)  # Default to same day if not specified
        leave_type = parsed_info.get("leave_type")
        reason = parsed_info.get("reason")
        half_day = parsed_info.get("half_day", False)

        logger.info(f"LLM parsed leave request - Start: {start_date}, End: {end_date}, Type: {leave_type}, Reason: {reason}")

        # Use the standard request_leave function with parsed parameters
        result = request_leave(
            start_date=start_date,
            end_date=end_date,
            leave_type=leave_type,
            reason=reason,
            half_day=half_day,
            tool_context=tool_context
        )

        # Add intelligent parsing information to the response
        if result.get("status") == "success":
            result["intelligent_parsing"] = {
                "original_request": leave_request_text,
                "parsed_start_date": start_date,
                "parsed_end_date": end_date,
                "parsed_leave_type": leave_type,
                "parsed_reason": reason,
                "interpretation": parsed_info.get("interpretation", ""),
                "parsing_method": "llm_powered"
            }

        return result

    except Exception as e:
        logger.error(f"Error processing LLM-powered leave request: {e}")
        return {
            "status": "error",
            "message": f"Error processing leave request: {str(e)}"
        }

def _llm_parse_leave_request(text: str, tool_context: Optional[object] = None) -> Dict[str, Any]:
    """
    Use LLM to intelligently parse natural language leave request text.

    This function leverages AI to understand natural language and extract:
    - Dates (relative, absolute, natural language)
    - Leave types (sick, annual, personal, etc.)
    - Reasons and context
    - Duration and timing preferences

    Args:
        text: Natural language leave request text
        tool_context: Tool context for accessing session state

    Returns:
        Dictionary containing parsed leave request information
    """
    try:
        logger.info(f"Using LLM to parse leave request: '{text}'")

        # Try to use Google ADK for intelligent parsing if available
        try:
            # Import Google ADK LLM components
            from google.adk.models.google_llm import Gemini
            from datetime import datetime, timedelta
            import os

            logger.info("Google ADK successfully imported for LLM parsing")

            # Get current date for context
            current_date = datetime.now().strftime("%Y-%m-%d")
            current_day = datetime.now().strftime("%A")

            # Create the LLM prompt for intelligent parsing
            parsing_prompt = f"""
You are an intelligent HR assistant that parses natural language leave requests into structured data.

Current date: {current_date} ({current_day})

Parse the following leave request and extract the information in JSON format:

Leave Request: "{text}"

Extract the following information:
1. start_date: The start date in YYYY-MM-DD format
2. end_date: The end date in YYYY-MM-DD format (same as start_date if single day)
3. leave_type: Type of leave (sick, annual, personal, casual, maternity, paternity, bereavement, emergency)
4. reason: The reason for the leave
5. half_day: Boolean indicating if it's a half-day leave
6. interpretation: A human-readable interpretation of what was understood

Rules for date parsing:
- "next Tuesday" means the Tuesday of next week
- "coming Tuesday" means the next Tuesday (could be this week or next week)
- "tomorrow" means {(datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d")}
- "today" means {current_date}
- For day names without qualifiers, assume the next occurrence of that day

Rules for leave type detection:
- Look for keywords like "sick", "ill", "unwell", "medical" → sick leave
- Look for "vacation", "holiday", "annual" → annual leave
- Look for "personal", "family", "emergency" → personal leave
- Look for "casual", "day off" → casual leave

Return ONLY a valid JSON object with the extracted information. If any information cannot be determined, use null for that field.

Example response:
{{
    "success": true,
    "start_date": "2024-06-04",
    "end_date": "2024-06-04",
    "leave_type": "sick",
    "reason": "Not feeling well",
    "half_day": false,
    "interpretation": "Requesting sick leave on Tuesday, June 4th, 2024 - Not feeling well"
}}
"""

            # Check for Google API key
            api_key = os.environ.get("GOOGLE_API_KEY")
            if not api_key:
                raise ImportError("GOOGLE_API_KEY not found in environment")

            # Initialize the Gemini model with proper configuration
            gemini_model = Gemini(
                model_name="gemini-2.0-flash-exp",
                api_key=api_key
            )

            # Get the LLM response (using async method)
            import asyncio
            response = asyncio.run(gemini_model.generate_content_async(parsing_prompt))

            # Parse the JSON response
            import json
            import re

            # Extract JSON from the response
            response_text = response.text if hasattr(response, 'text') else str(response)

            # Try to find JSON in the response
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group(0)
                parsed_result = json.loads(json_str)

                # Validate the parsed result
                if parsed_result.get("success") and parsed_result.get("start_date"):
                    logger.info(f"LLM successfully parsed leave request: {parsed_result}")
                    return parsed_result
                else:
                    logger.warning(f"LLM parsing incomplete: {parsed_result}")
                    return {
                        "success": False,
                        "error": "LLM could not fully parse the leave request",
                        "llm_response": parsed_result
                    }
            else:
                logger.error(f"No valid JSON found in LLM response: {response_text}")
                return {
                    "success": False,
                    "error": "LLM response did not contain valid JSON",
                    "llm_response": response_text
                }

        except ImportError as e:
            logger.info(f"Google ADK not available ({e}), falling back to enhanced parsing")
        except Exception as e:
            logger.warning(f"Google ADK LLM parsing failed: {e}, falling back to enhanced parsing")

        # Fallback to enhanced parsing if LLM is not available
        logger.info("Using enhanced parsing as fallback")
        return _enhanced_parse_leave_request(text)

    except Exception as e:
        logger.error(f"Error in LLM parsing: {e}")

        # Fallback to basic parsing if everything fails
        logger.info("Falling back to basic parsing due to error")
        return _basic_parse_leave_request(text)

def _enhanced_parse_leave_request(text: str) -> Dict[str, Any]:
    """
    Enhanced parsing with better natural language understanding.

    This function uses improved pattern matching and context analysis
    to parse leave requests more intelligently than basic parsing.

    Args:
        text: Natural language leave request text

    Returns:
        Dictionary containing parsed leave request information
    """
    try:
        from datetime import datetime, timedelta
        import re

        logger.info(f"Using enhanced parsing for: '{text}'")

        # Normalize text
        normalized_text = text.lower().strip()

        # Enhanced date parsing with better context understanding
        today = datetime.now().date()
        start_date = None

        # More sophisticated date pattern matching
        date_patterns = [
            # Relative dates with context
            (r'\b(tomorrow)\b', lambda m: today + timedelta(days=1)),
            (r'\b(today)\b', lambda m: today),
            (r'\b(next|coming)\s+(monday|tuesday|wednesday|thursday|friday|saturday|sunday)\b',
             lambda m: _calculate_next_weekday(today, m.group(2))),
            (r'\b(this)\s+(monday|tuesday|wednesday|thursday|friday|saturday|sunday)\b',
             lambda m: _calculate_this_weekday(today, m.group(2))),
            (r'\b(monday|tuesday|wednesday|thursday|friday|saturday|sunday)\b',
             lambda m: _calculate_next_weekday(today, m.group(1))),
        ]

        for pattern, date_func in date_patterns:
            match = re.search(pattern, normalized_text)
            if match:
                try:
                    calculated_date = date_func(match)
                    start_date = calculated_date.strftime("%Y-%m-%d")
                    logger.info(f"Enhanced parsing found date: {start_date} from pattern: {pattern}")
                    break
                except Exception as e:
                    logger.warning(f"Error calculating date for pattern {pattern}: {e}")
                    continue

        if not start_date:
            return {
                "success": False,
                "error": "Could not determine the date from the request"
            }

        # Enhanced leave type detection with context
        leave_type = _detect_leave_type_enhanced(normalized_text)
        if not leave_type:
            leave_type = "personal"  # Default fallback

        # Enhanced reason extraction
        reason = _extract_reason_enhanced(text, leave_type)

        # Check for half-day indicators
        half_day = any(indicator in normalized_text for indicator in
                      ["half day", "half-day", "morning only", "afternoon only", "partial day"])

        return {
            "success": True,
            "start_date": start_date,
            "end_date": start_date,
            "leave_type": leave_type,
            "reason": reason,
            "half_day": half_day,
            "interpretation": f"Requesting {leave_type} leave on {start_date} - {reason}",
            "parsing_method": "enhanced_fallback"
        }

    except Exception as e:
        logger.error(f"Error in enhanced parsing: {e}")
        return {
            "success": False,
            "error": f"Error in enhanced parsing: {str(e)}"
        }

def _calculate_next_weekday(today, weekday_name):
    """Calculate the next occurrence of a weekday."""
    weekdays = {
        'monday': 0, 'tuesday': 1, 'wednesday': 2, 'thursday': 3,
        'friday': 4, 'saturday': 5, 'sunday': 6
    }

    target_weekday = weekdays.get(weekday_name.lower())
    if target_weekday is None:
        raise ValueError(f"Invalid weekday: {weekday_name}")

    current_weekday = today.weekday()
    days_ahead = target_weekday - current_weekday

    if days_ahead <= 0:  # Target day already passed this week
        days_ahead += 7

    return today + timedelta(days=days_ahead)

def _calculate_this_weekday(today, weekday_name):
    """Calculate this week's occurrence of a weekday."""
    weekdays = {
        'monday': 0, 'tuesday': 1, 'wednesday': 2, 'thursday': 3,
        'friday': 4, 'saturday': 5, 'sunday': 6
    }

    target_weekday = weekdays.get(weekday_name.lower())
    if target_weekday is None:
        raise ValueError(f"Invalid weekday: {weekday_name}")

    current_weekday = today.weekday()
    days_ahead = target_weekday - current_weekday

    if days_ahead < 0:  # Day already passed this week, assume next week
        days_ahead += 7

    return today + timedelta(days=days_ahead)

def _detect_leave_type_enhanced(text):
    """Enhanced leave type detection with better context understanding."""
    # More sophisticated leave type patterns with scoring
    leave_patterns = {
        "sick": {
            "patterns": [r"\b(sick|illness|unwell|not feeling well|medical|doctor|hospital|health)\b"],
            "score": 0
        },
        "annual": {
            "patterns": [r"\b(annual|vacation|holiday|time off)\b"],
            "score": 0
        },
        "personal": {
            "patterns": [r"\b(personal|family|emergency|urgent|private)\b"],
            "score": 0
        },
        "casual": {
            "patterns": [r"\b(casual|day off)\b"],
            "score": 0
        }
    }

    # Score each leave type based on pattern matches
    for leave_type, config in leave_patterns.items():
        for pattern in config["patterns"]:
            matches = len(re.findall(pattern, text))
            config["score"] += matches

    # Return the highest scoring leave type
    best_type = max(leave_patterns.items(), key=lambda x: x[1]["score"])
    return best_type[0] if best_type[1]["score"] > 0 else None

def _extract_reason_enhanced(text, leave_type):
    """Enhanced reason extraction with better context understanding."""
    import re

    # Try to extract reason from common patterns
    reason_patterns = [
        r"because\s+(.+?)(?:\.|,|$)",
        r"as\s+(.+?)(?:\.|,|$)",
        r"since\s+(.+?)(?:\.|,|$)",
        r"for\s+(.+?)(?:\.|,|$)",
        r"due to\s+(.+?)(?:\.|,|$)"
    ]

    for pattern in reason_patterns:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            reason = match.group(1).strip()
            # Clean up the reason
            reason = re.sub(r'\s+', ' ', reason)
            if len(reason) > 5:  # Ensure it's a meaningful reason
                return reason

    # Generate contextual reason based on leave type and text content
    if leave_type == "sick":
        if any(word in text.lower() for word in ["doctor", "appointment", "medical"]):
            return "Medical appointment"
        elif any(word in text.lower() for word in ["unwell", "not feeling well", "sick"]):
            return "Not feeling well"
        else:
            return "Health reasons"
    elif leave_type == "personal":
        if "family" in text.lower():
            return "Family matters"
        elif "emergency" in text.lower():
            return "Personal emergency"
        else:
            return "Personal reasons"
    elif leave_type == "annual":
        return "Annual leave"
    else:
        return f"{leave_type.title()} leave request"

def _basic_parse_leave_request(text: str) -> Dict[str, Any]:
    """
    Basic fallback parsing when LLM is not available.

    Args:
        text: Natural language leave request text

    Returns:
        Dictionary containing parsed leave request information
    """
    try:
        from datetime import datetime, timedelta
        import re

        logger.info(f"Using basic parsing for: '{text}'")

        # Normalize text
        normalized_text = text.lower().strip()

        # Basic date parsing
        today = datetime.now().date()
        start_date = None

        # Look for common date patterns
        if "tomorrow" in normalized_text:
            start_date = (today + timedelta(days=1)).strftime("%Y-%m-%d")
        elif "today" in normalized_text:
            start_date = today.strftime("%Y-%m-%d")
        elif "next tuesday" in normalized_text or "coming tuesday" in normalized_text:
            # Find next Tuesday
            days_ahead = 1 - today.weekday()  # Tuesday is 1
            if days_ahead <= 0:
                days_ahead += 7
            start_date = (today + timedelta(days=days_ahead)).strftime("%Y-%m-%d")
        elif "next friday" in normalized_text or "coming friday" in normalized_text:
            # Find next Friday
            days_ahead = 4 - today.weekday()  # Friday is 4
            if days_ahead <= 0:
                days_ahead += 7
            start_date = (today + timedelta(days=days_ahead)).strftime("%Y-%m-%d")

        if not start_date:
            return {
                "success": False,
                "error": "Could not determine the date from the request"
            }

        # Basic leave type detection
        leave_type = None
        if any(word in normalized_text for word in ["sick", "ill", "unwell", "not feeling well"]):
            leave_type = "sick"
        elif any(word in normalized_text for word in ["annual", "vacation", "holiday"]):
            leave_type = "annual"
        elif any(word in normalized_text for word in ["personal", "family"]):
            leave_type = "personal"
        elif any(word in normalized_text for word in ["casual", "day off"]):
            leave_type = "casual"

        if not leave_type:
            leave_type = "personal"  # Default fallback

        # Basic reason extraction
        reason = "Leave request"
        if "because" in normalized_text:
            reason_match = re.search(r"because\s+(.+?)(?:\.|$)", normalized_text)
            if reason_match:
                reason = reason_match.group(1).strip()
        elif "as" in normalized_text:
            reason_match = re.search(r"as\s+(.+?)(?:\.|$)", normalized_text)
            if reason_match:
                reason = reason_match.group(1).strip()

        return {
            "success": True,
            "start_date": start_date,
            "end_date": start_date,
            "leave_type": leave_type,
            "reason": reason,
            "half_day": "half" in normalized_text,
            "interpretation": f"Requesting {leave_type} leave on {start_date} - {reason}",
            "parsing_method": "basic_fallback"
        }

    except Exception as e:
        logger.error(f"Error in basic parsing: {e}")
        return {
            "success": False,
            "error": f"Error in basic parsing: {str(e)}"
        }

# Old hardcoded helper functions removed - now using LLM-powered intelligent parsing
