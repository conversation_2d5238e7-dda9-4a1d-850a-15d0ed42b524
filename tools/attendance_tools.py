"""
Unified Attendance Management Tools Module.

This module provides comprehensive attendance management functionality using the
Flexible WagonHR Agent for real-time API integration. All attendance-related
operations are consolidated here for better organization and maintainability.

Replaces both hr_api.py attendance functions and old attendance_tools.py mock functions
with a single, unified module that uses real WagonHR APIs.
"""

import logging
import datetime
from typing import Dict, Any, Optional, List

from google.adk.tools.tool_context import ToolContext

# Set up logger
logger = logging.getLogger(__name__)

# Attendance status types
ATTENDANCE_STATUS = {
    "present": "Present",
    "absent": "Absent", 
    "late": "Late",
    "half_day": "Half Day",
    "work_from_home": "Work From Home",
    "on_leave": "On Leave",
    "holiday": "Holiday",
    "checkin": "Check In",
    "checkout": "Check Out"
}

def record_attendance(
    employee_id: Optional[str] = None,
    email: Optional[str] = None,
    status: Optional[str] = None,
    timestamp: Optional[str] = None,
    location: Optional[dict] = None,
    notes: Optional[str] = None,
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Records attendance for an employee using WagonHR API.

    This tool directly calls the WagonHR attendance recording endpoint.
    The API automatically identifies the employee using the auth token.

    Args:
        employee_id: Optional employee ID (for compatibility)
        email: Optional employee email (for compatibility)
        status: Attendance status (present, absent, late, checkin, checkout, etc.)
        timestamp: ISO format timestamp (YYYY-MM-DDTHH:MM:SS)
        location: Optional location coordinates {"lat": float, "lng": float}
        notes: Optional notes about the attendance
        tool_context: Tool context for accessing session state

    Returns:
        Dictionary containing operation status and attendance record details
    """
    try:
        logger.info("=== RECORD ATTENDANCE ===")
        logger.info("Calling WagonHR attendance recording endpoint directly")

        # Validate required parameters
        if not status:
            return {
                "status": "error",
                "message": "status is required"
            }

        # Validate status
        if status.lower() not in [s.lower() for s in ATTENDANCE_STATUS.keys()]:
            return {
                "status": "error",
                "message": f"Invalid attendance status: {status}. Valid statuses are: {', '.join(ATTENDANCE_STATUS.keys())}"
            }

        # Import required modules
        import requests
        from utils import hr_auth

        # Prepare the API call
        base_url = "https://dev-api-wagonhr.mouritech.net"
        endpoint = "/api/hrms/attendance/record"
        url = f"{base_url}{endpoint}"

        # Prepare headers
        headers = {
            "Authorization": f"Bearer {hr_auth.wagonhr_access_token}",
            "Content-Type": "application/json",
            "Accept": "application/json",
            "x-tenantid": "mouritech",
            "x-entityid": "US-MT-002"
        }

        # Prepare request payload
        payload = {
            "status": status.lower(),
            "timestamp": timestamp or datetime.datetime.now().isoformat()
        }

        if location:
            payload["location"] = location
        if notes:
            payload["notes"] = notes

        logger.info(f"Making POST request to: {url}")
        logger.info(f"Payload: {payload}")

        # Make the API call
        response = requests.post(url, json=payload, headers=headers, timeout=30)

        logger.info(f"Response status: {response.status_code}")

        if response.status_code in [200, 201]:
            data = response.json()
            logger.info("Attendance recorded successfully")

            return {
                "status": "success",
                "message": "Attendance recorded successfully",
                "attendance_record": data
            }
        else:
            error_msg = f"API call failed with status {response.status_code}"
            try:
                error_data = response.json()
                error_msg += f": {error_data.get('message', 'Unknown error')}"
            except:
                error_msg += f": {response.text}"

            logger.error(error_msg)
            return {
                "status": "error",
                "message": error_msg
            }

    except Exception as e:
        logger.error(f"Error recording attendance: {e}")
        return {
            "status": "error",
            "message": f"Error recording attendance: {str(e)}"
        }

def get_attendance_report(
    employee_email: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Retrieves attendance report for an employee using WagonHR API.

    This tool directly calls the WagonHR attendance report endpoint.

    Args:
        employee_email: Email of the employee (optional, uses authenticated user if not provided)
        start_date: Start date of the report (YYYY-MM-DD)
        end_date: End date of the report (YYYY-MM-DD)
        tool_context: Tool context for accessing session state

    Returns:
        Dictionary containing operation status and attendance report details
    """
    try:
        logger.info("=== GET ATTENDANCE REPORT ===")
        logger.info("Calling WagonHR attendance report endpoint directly")

        # Validate required parameters
        if not start_date or not end_date:
            return {
                "status": "error",
                "message": "start_date and end_date are required"
            }

        # Import required modules
        import requests
        from utils import hr_auth

        # Prepare the API call
        base_url = "https://dev-api-wagonhr.mouritech.net"
        endpoint = "/api/hrms/attendance/report"
        url = f"{base_url}{endpoint}"

        # Prepare headers
        headers = {
            "Authorization": f"Bearer {hr_auth.wagonhr_access_token}",
            "Content-Type": "application/json",
            "Accept": "application/json",
            "x-tenantid": "mouritech",
            "x-entityid": "US-MT-002"
        }

        # Prepare query parameters
        params = {
            "startDate": start_date,
            "endDate": end_date
        }

        if employee_email:
            params["employeeEmail"] = employee_email

        logger.info(f"Making GET request to: {url}")
        logger.info(f"Query parameters: {params}")

        # Make the API call
        response = requests.get(url, headers=headers, params=params, timeout=30)

        logger.info(f"Response status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            logger.info("Attendance report retrieved successfully")

            return {
                "status": "success",
                "message": "Attendance report retrieved successfully",
                "report": data
            }
        else:
            error_msg = f"API call failed with status {response.status_code}"
            try:
                error_data = response.json()
                error_msg += f": {error_data.get('message', 'Unknown error')}"
            except Exception:
                error_msg += f": {response.text}"

            logger.error(error_msg)
            return {
                "status": "error",
                "message": error_msg
            }

    except Exception as e:
        logger.error(f"Error retrieving attendance report: {e}")
        return {
            "status": "error",
            "message": f"Error retrieving attendance report: {str(e)}"
        }

def check_in(
    location: Optional[dict] = None,
    notes: Optional[str] = None,
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Records check-in attendance using WagonHR API.
    
    Args:
        location: Optional location coordinates {"lat": float, "lng": float}
        notes: Optional notes about the check-in
        tool_context: Tool context for accessing session state
        
    Returns:
        Dictionary containing operation status and check-in details
    """
    return record_attendance(
        status="checkin",
        location=location,
        notes=notes,
        tool_context=tool_context
    )

def check_out(
    location: Optional[dict] = None,
    notes: Optional[str] = None,
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Records check-out attendance using WagonHR API.
    
    Args:
        location: Optional location coordinates {"lat": float, "lng": float}
        notes: Optional notes about the check-out
        tool_context: Tool context for accessing session state
        
    Returns:
        Dictionary containing operation status and check-out details
    """
    return record_attendance(
        status="checkout",
        location=location,
        notes=notes,
        tool_context=tool_context
    )

def get_attendance_summary(
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Retrieves attendance summary for the authenticated employee using WagonHR API.

    This tool directly calls the WagonHR attendance summary endpoint.

    Args:
        start_date: Start date for summary (YYYY-MM-DD)
        end_date: End date for summary (YYYY-MM-DD)
        tool_context: Tool context for accessing session state

    Returns:
        Dictionary containing operation status and attendance summary
    """
    try:
        logger.info("=== GET ATTENDANCE SUMMARY ===")
        logger.info("Calling WagonHR attendance summary endpoint directly")

        # Import required modules
        import requests
        from utils import hr_auth

        # Prepare the API call
        base_url = "https://dev-api-wagonhr.mouritech.net"
        endpoint = "/api/hrms/attendance/summary"
        url = f"{base_url}{endpoint}"

        # Prepare headers
        headers = {
            "Authorization": f"Bearer {hr_auth.wagonhr_access_token}",
            "Content-Type": "application/json",
            "Accept": "application/json",
            "x-tenantid": "mouritech",
            "x-entityid": "US-MT-002"
        }

        # Prepare query parameters
        params = {}
        if start_date:
            params["startDate"] = start_date
        if end_date:
            params["endDate"] = end_date

        logger.info(f"Making GET request to: {url}")
        if params:
            logger.info(f"Query parameters: {params}")

        # Make the API call
        response = requests.get(url, headers=headers, params=params, timeout=30)

        logger.info(f"Response status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            logger.info("Attendance summary retrieved successfully")

            return {
                "status": "success",
                "message": "Attendance summary retrieved successfully",
                "summary": data
            }
        else:
            error_msg = f"API call failed with status {response.status_code}"
            try:
                error_data = response.json()
                error_msg += f": {error_data.get('message', 'Unknown error')}"
            except Exception:
                error_msg += f": {response.text}"

            logger.error(error_msg)
            return {
                "status": "error",
                "message": error_msg
            }

    except Exception as e:
        logger.error(f"Error retrieving attendance summary: {e}")
        return {
            "status": "error",
            "message": f"Error retrieving attendance summary: {str(e)}"
        }
