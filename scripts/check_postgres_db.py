#!/usr/bin/env python
"""
Check PostgreSQL database script.

This script checks if the PostgreSQL database is properly configured and accessible.
"""

import os
import sys
import logging
import traceback
from typing import Dict, Any, Optional

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Set up logger
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_postgres_db():
    """Check if the PostgreSQL database is properly configured and accessible."""
    try:
        # Import database configuration
        from utils.db_config import DB_URL, engine, SCHEMA_NAME
        
        logger.info(f"Database URL: {DB_URL}")
        
        # Check if using PostgreSQL
        if not DB_URL.startswith("postgresql"):
            logger.error("Not using PostgreSQL. Please update DB_URL in utils/db_config.py")
            return False
        
        # Try to connect to PostgreSQL
        try:
            from sqlalchemy import text
            with engine.connect() as connection:
                # Check if schema exists
                logger.info(f"Checking if schema '{SCHEMA_NAME}' exists")
                result = connection.execute(text(f"SELECT schema_name FROM information_schema.schemata WHERE schema_name = '{SCHEMA_NAME}'"))
                schema_exists = result.fetchone() is not None
                
                if schema_exists:
                    logger.info(f"Schema '{SCHEMA_NAME}' exists")
                else:
                    logger.warning(f"Schema '{SCHEMA_NAME}' does not exist")
                    
                    # Create schema if it doesn't exist
                    logger.info(f"Creating schema '{SCHEMA_NAME}'")
                    connection.execute(text(f"CREATE SCHEMA IF NOT EXISTS {SCHEMA_NAME}"))
                    connection.commit()
                    logger.info(f"Schema '{SCHEMA_NAME}' created successfully")
                
                # Check if tables exist
                logger.info(f"Checking if tables exist in schema '{SCHEMA_NAME}'")
                result = connection.execute(text(f"""
                    SELECT table_name 
                    FROM information_schema.tables 
                    WHERE table_schema = '{SCHEMA_NAME}'
                """))
                tables = [row[0] for row in result.fetchall()]
                
                if tables:
                    logger.info(f"Found {len(tables)} tables in schema '{SCHEMA_NAME}': {', '.join(tables)}")
                else:
                    logger.warning(f"No tables found in schema '{SCHEMA_NAME}'")
                    
                    # Initialize database
                    logger.info("Initializing database")
                    from utils.db_config import init_db
                    init_db()
                    
                    # Check tables again
                    result = connection.execute(text(f"""
                        SELECT table_name 
                        FROM information_schema.tables 
                        WHERE table_schema = '{SCHEMA_NAME}'
                    """))
                    tables = [row[0] for row in result.fetchall()]
                    
                    if tables:
                        logger.info(f"Created {len(tables)} tables in schema '{SCHEMA_NAME}': {', '.join(tables)}")
                    else:
                        logger.error(f"Failed to create tables in schema '{SCHEMA_NAME}'")
                        return False
                
                # Test inserting a record
                logger.info("Testing database write operations")
                
                # Import models
                from models.audit_models import AgentRegistry
                from utils.db_config import get_db_session
                
                # Get database session
                db = get_db_session()
                try:
                    # Check if test agent exists
                    test_agent = db.query(AgentRegistry).filter_by(name="test_agent").first()
                    
                    if test_agent:
                        logger.info(f"Test agent already exists with ID {test_agent.id}")
                    else:
                        # Create test agent
                        logger.info("Creating test agent")
                        test_agent = AgentRegistry(
                            name="test_agent",
                            description="Test agent for database check",
                            is_active=True
                        )
                        db.add(test_agent)
                        db.commit()
                        db.refresh(test_agent)
                        logger.info(f"Created test agent with ID {test_agent.id}")
                finally:
                    db.close()
                
                logger.info("Database check passed")
                return True
        except Exception as e:
            logger.error(f"Error connecting to PostgreSQL database: {str(e)}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            return False
    except Exception as e:
        logger.error(f"Error checking database configuration: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    if check_postgres_db():
        logger.info("PostgreSQL database check passed")
        sys.exit(0)
    else:
        logger.error("PostgreSQL database check failed")
        sys.exit(1)
