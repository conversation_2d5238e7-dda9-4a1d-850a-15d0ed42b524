#!/usr/bin/env python
"""
Check database configuration script.

This script checks the database configuration and ensures the necessary
dependencies are installed.
"""

import os
import sys
import logging
import importlib.util

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Set up logger
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_db_config():
    """Check database configuration and dependencies."""
    # Check if psycopg2 is installed
    psycopg2_installed = importlib.util.find_spec("psycopg2") is not None
    if not psycopg2_installed:
        psycopg2_binary_installed = importlib.util.find_spec("psycopg2_binary") is not None
        if not psycopg2_binary_installed:
            logger.warning("PostgreSQL adapter (psycopg2/psycopg2-binary) is not installed.")
            logger.warning("You may need to install it using: pip install psycopg2-binary")
            logger.warning("Or switch to SQLite by updating utils/db_config.py")
        else:
            logger.info("PostgreSQL adapter (psycopg2-binary) is installed.")
    else:
        logger.info("PostgreSQL adapter (psycopg2) is installed.")
    
    # Check database URL from environment
    db_url = os.environ.get("DATABASE_URL")
    if db_url:
        logger.info(f"Database URL from environment: {db_url}")
        if db_url.startswith("postgresql"):
            if not psycopg2_installed and not psycopg2_binary_installed:
                logger.error("PostgreSQL URL is configured but PostgreSQL adapter is not installed!")
                logger.error("Please install psycopg2-binary or update the database URL to use SQLite.")
                return False
    else:
        logger.info("No DATABASE_URL found in environment.")
        logger.info("Using default database configuration from utils/db_config.py")
    
    # Import database configuration
    try:
        from utils.db_config import DB_URL, engine
        logger.info(f"Database URL from config: {DB_URL}")
        
        # Check if using PostgreSQL
        if DB_URL.startswith("postgresql"):
            if not psycopg2_installed and not psycopg2_binary_installed:
                logger.error("PostgreSQL URL is configured but PostgreSQL adapter is not installed!")
                logger.error("Please install psycopg2-binary or update the database URL to use SQLite.")
                return False
            
            # Try to connect to PostgreSQL
            try:
                connection = engine.connect()
                connection.close()
                logger.info("Successfully connected to PostgreSQL database.")
            except Exception as e:
                logger.error(f"Error connecting to PostgreSQL database: {str(e)}")
                logger.error("Please check your PostgreSQL connection or update the database URL to use SQLite.")
                return False
        elif DB_URL.startswith("sqlite"):
            logger.info("Using SQLite database.")
        else:
            logger.warning(f"Unknown database type: {DB_URL}")
        
        return True
    except ImportError:
        logger.error("Error importing database configuration.")
        return False
    except Exception as e:
        logger.error(f"Error checking database configuration: {str(e)}")
        return False

if __name__ == "__main__":
    if check_db_config():
        logger.info("Database configuration check passed.")
    else:
        logger.error("Database configuration check failed.")
        sys.exit(1)
