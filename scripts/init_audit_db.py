#!/usr/bin/env python
"""
Initialize the audit database.

This script initializes the audit database and creates sample data for testing.
"""

import os
import sys
import logging
import datetime
import uuid
from typing import Dict, Any, List

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.db_config import init_db, get_db_session
from models.audit_models import (
    AgentRegistry,
    AgentPrompt,
    AgentTool,
    Session,
    SessionMessage,
    AuditEvent
)

# Set up logger
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_sample_data():
    """Create sample data for testing."""
    db = get_db_session()
    
    try:
        # Create agent
        agent = AgentRegistry(
            name="ITSM Agent",
            description="IT Service Management Agent",
            is_active=True,
            config={
                "model": "claude-3-sonnet-20240229",
                "temperature": 0.7,
                "max_tokens": 4096
            }
        )
        db.add(agent)
        db.flush()
        agent_id = agent.id
        logger.info(f"Created agent with ID {agent_id}")
        
        # Create prompts
        system_prompt = AgentPrompt(
            agent_id=agent_id,
            prompt_type="system",
            prompt_text="You are an IT Service Management assistant. Your role is to help users with IT-related issues."
        )
        db.add(system_prompt)
        
        # Create tools
        tools = [
            {
                "name": "reset_password",
                "description": "Reset a user's password in Active Directory"
            },
            {
                "name": "create_ticket",
                "description": "Create a ticket in the ITSM system"
            },
            {
                "name": "update_ticket",
                "description": "Update a ticket in the ITSM system"
            },
            {
                "name": "get_user_info",
                "description": "Get information about a user from Active Directory"
            }
        ]
        
        for tool_data in tools:
            tool = AgentTool(
                agent_id=agent_id,
                tool_name=tool_data["name"],
                tool_description=tool_data["description"]
            )
            db.add(tool)
        
        # Create session
        session_id = str(uuid.uuid4())
        session = Session(
            id=session_id,
            user_identifier="<EMAIL>",
            agent_id=agent_id,
            start_time=datetime.datetime.utcnow() - datetime.timedelta(minutes=30),
            end_time=datetime.datetime.utcnow() - datetime.timedelta(minutes=5)
        )
        db.add(session)
        
        # Create messages
        messages = [
            {
                "actor_type": "user",
                "message": "I need to reset my password"
            },
            {
                "actor_type": "agent",
                "message": "I can help you reset your password. Can you please confirm your email address?"
            },
            {
                "actor_type": "user",
                "message": "My <NAME_EMAIL>"
            },
            {
                "actor_type": "agent",
                "message": "Thank you. I'll reset your password now."
            },
            {
                "actor_type": "agent",
                "message": "Your password has been reset. You will receive an email with your temporary password shortly."
            },
            {
                "actor_type": "user",
                "message": "Thank you!"
            }
        ]
        
        timestamp = session.start_time
        for message_data in messages:
            timestamp += datetime.timedelta(minutes=5)
            message = SessionMessage(
                session_id=session_id,
                actor_type=message_data["actor_type"],
                message=message_data["message"],
                timestamp=timestamp
            )
            db.add(message)
        
        # Create audit events
        events = [
            {
                "actor_type": "user",
                "action_type": "user_input",
                "status": "success",
                "context_data": {"message": "I need to reset my password"}
            },
            {
                "actor_type": "agent",
                "action_type": "agent_response",
                "status": "success",
                "context_data": {"message": "I can help you reset your password. Can you please confirm your email address?"}
            },
            {
                "actor_type": "user",
                "action_type": "user_input",
                "status": "success",
                "context_data": {"message": "My <NAME_EMAIL>"}
            },
            {
                "actor_type": "tool",
                "actor_id": "get_user_info",
                "action_type": "tool_call",
                "status": "success",
                "entity_type": "user",
                "entity_id": "john.doe",
                "context_data": {"args": {"email": "<EMAIL>"}}
            },
            {
                "actor_type": "tool",
                "actor_id": "reset_password",
                "action_type": "tool_call",
                "status": "success",
                "entity_type": "password",
                "entity_id": "john.doe",
                "context_data": {"args": {"email": "<EMAIL>"}}
            },
            {
                "actor_type": "tool",
                "actor_id": "create_ticket",
                "action_type": "tool_call",
                "status": "success",
                "entity_type": "ticket",
                "entity_id": "INC-12345",
                "context_data": {"args": {"subject": "Password Reset", "description": "Reset <NAME_EMAIL>"}}
            },
            {
                "actor_type": "agent",
                "action_type": "agent_response",
                "status": "success",
                "context_data": {"message": "Thank you. I'll reset your password now."}
            },
            {
                "actor_type": "agent",
                "action_type": "agent_response",
                "status": "success",
                "context_data": {"message": "Your password has been reset. You will receive an email with your temporary password shortly."}
            },
            {
                "actor_type": "user",
                "action_type": "user_input",
                "status": "success",
                "context_data": {"message": "Thank you!"}
            }
        ]
        
        timestamp = session.start_time
        for event_data in events:
            timestamp += datetime.timedelta(minutes=5)
            event = AuditEvent(
                session_id=session_id,
                agent_id=agent_id,
                user_identifier="<EMAIL>",
                actor_type=event_data["actor_type"],
                actor_id=event_data.get("actor_id"),
                action_type=event_data["action_type"],
                entity_type=event_data.get("entity_type"),
                entity_id=event_data.get("entity_id"),
                status=event_data["status"],
                context_data=event_data.get("context_data"),
                created_at=timestamp
            )
            db.add(event)
        
        # Commit changes
        db.commit()
        logger.info("Sample data created successfully")
    except Exception as e:
        db.rollback()
        logger.error(f"Error creating sample data: {str(e)}")
    finally:
        db.close()


def main():
    """Initialize the database."""
    try:
        # Initialize database
        init_db()
        logger.info("Database initialized successfully")
        
        # Create sample data
        create_sample_data()
    except Exception as e:
        logger.error(f"Error initializing database: {str(e)}")


if __name__ == "__main__":
    main()
