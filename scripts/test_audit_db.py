#!/usr/bin/env python
"""
Test audit database script.

This script tests the audit database by creating a test agent, session, and audit event.
"""

import os
import sys
import logging
import uuid
import traceback
from typing import Dict, Any, Optional

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Set up logger
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_audit_db():
    """Test the audit database by creating a test agent, session, and audit event."""
    try:
        # Import database configuration
        from utils.db_config import init_db
        from services.audit_service import AuditService
        
        # Initialize database
        logger.info("Initializing database")
        init_db()
        
        # Create test agent
        logger.info("Creating test agent")
        agent_id = AuditService.register_agent(
            name="test_agent",
            description="Test agent for audit database test",
            config={"test": True}
        )
        
        if agent_id:
            logger.info(f"Created test agent with ID {agent_id}")
        else:
            logger.error("Failed to create test agent")
            return False
        
        # Create test session
        logger.info("Creating test session")
        session_id = str(uuid.uuid4())
        user_id = "<EMAIL>"
        
        session_created = AuditService.create_session(
            session_id=session_id,
            user_identifier=user_id,
            agent_id=agent_id
        )
        
        if session_created:
            logger.info(f"Created test session with ID {session_id}")
        else:
            logger.error("Failed to create test session")
            return False
        
        # Create test audit event
        logger.info("Creating test audit event")
        event_id = AuditService.record_audit_event(
            session_id=session_id,
            agent_id=agent_id,
            user_identifier=user_id,
            actor_type="tool",
            actor_id="test_tool",
            action_type="tool_result",
            entity_type="password",
            entity_id="test_user",
            status="success",
            error_message=None,
            context_data={
                "args": {"user_identifier": "test_user", "requesting_user_email": "<EMAIL>", "is_admin": True},
                "result": {"status": "success", "username": "test_user", "email": "<EMAIL>", "message": "Password reset successful"}
            }
        )
        
        if event_id:
            logger.info(f"Created test audit event with ID {event_id}")
        else:
            logger.error("Failed to create test audit event")
            return False
        
        # Get audit events
        logger.info("Getting audit events")
        events = AuditService.get_audit_events(session_id=session_id)
        
        if events:
            logger.info(f"Found {len(events)} audit events for session {session_id}")
            for event in events:
                logger.info(f"Event: {event['id']} - Action: {event['action_type']} - Status: {event['status']}")
                logger.info(f"  Context data: {event['context_data']}")
        else:
            logger.error(f"No audit events found for session {session_id}")
            return False
        
        logger.info("Audit database test passed")
        return True
    except Exception as e:
        logger.error(f"Error testing audit database: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    if test_audit_db():
        logger.info("Audit database test passed")
        sys.exit(0)
    else:
        logger.error("Audit database test failed")
        sys.exit(1)
