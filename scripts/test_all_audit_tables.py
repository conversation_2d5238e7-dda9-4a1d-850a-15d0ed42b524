#!/usr/bin/env python
"""
Test all audit tables script.

This script tests all audit tables by creating test records in each table.
"""

import os
import sys
import logging
import uuid
import traceback
from typing import Dict, Any, Optional

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Set up logger
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_all_audit_tables():
    """Test all audit tables by creating test records in each table."""
    try:
        # Import database configuration
        from utils.db_config import init_db
        from services.audit_service import AuditService
        
        # Initialize database
        logger.info("Initializing database")
        init_db()
        
        # Create test agent
        logger.info("Creating test agent")
        agent_id = AuditService.register_agent(
            name="test_complete_agent",
            description="Test agent for complete audit database test",
            config={"test": True, "model": "test-model"}
        )
        
        if agent_id:
            logger.info(f"Created test agent with ID {agent_id}")
        else:
            logger.error("Failed to create test agent")
            return False
        
        # Create test agent prompt
        logger.info("Creating test agent prompt")
        prompt_id = AuditService.register_agent_prompt(
            agent_id=agent_id,
            prompt_type="system",
            prompt_text="You are a helpful assistant for testing the audit database."
        )
        
        if prompt_id:
            logger.info(f"Created test agent prompt with ID {prompt_id}")
        else:
            logger.error("Failed to create test agent prompt")
            return False
        
        # Create test agent tool
        logger.info("Creating test agent tool")
        tool_id = AuditService.register_agent_tool(
            agent_id=agent_id,
            tool_name="test_tool",
            tool_description="Test tool for audit database test",
            tool_config={"test": True}
        )
        
        if tool_id:
            logger.info(f"Created test agent tool with ID {tool_id}")
        else:
            logger.error("Failed to create test agent tool")
            return False
        
        # Create test session
        logger.info("Creating test session")
        session_id = str(uuid.uuid4())
        user_id = "<EMAIL>"
        
        session_created = AuditService.create_session(
            session_id=session_id,
            user_identifier=user_id,
            agent_id=agent_id
        )
        
        if session_created:
            logger.info(f"Created test session with ID {session_id}")
        else:
            logger.error("Failed to create test session")
            return False
        
        # Create test session messages
        logger.info("Creating test session messages")
        
        # User message
        user_message_id = AuditService.add_session_message(
            session_id=session_id,
            actor_type="user",
            message="Hello, I need help with testing the audit database."
        )
        
        if user_message_id:
            logger.info(f"Created test user message with ID {user_message_id}")
        else:
            logger.error("Failed to create test user message")
            return False
        
        # Agent message
        agent_message_id = AuditService.add_session_message(
            session_id=session_id,
            actor_type="agent",
            message="I'll help you test the audit database. What would you like to test?"
        )
        
        if agent_message_id:
            logger.info(f"Created test agent message with ID {agent_message_id}")
        else:
            logger.error("Failed to create test agent message")
            return False
        
        # Create test audit event
        logger.info("Creating test audit event")
        event_id = AuditService.record_audit_event(
            session_id=session_id,
            agent_id=agent_id,
            user_identifier=user_id,
            actor_type="tool",
            actor_id="test_tool",
            action_type="tool_result",
            entity_type="database",
            entity_id="audit_test",
            status="success",
            error_message=None,
            context_data={
                "args": {"test": True},
                "result": {"status": "success", "message": "Audit database test successful"}
            }
        )
        
        if event_id:
            logger.info(f"Created test audit event with ID {event_id}")
        else:
            logger.error("Failed to create test audit event")
            return False
        
        # Get session history
        logger.info("Getting session history")
        history = AuditService.get_session_history(
            session_id=session_id,
            include_messages=True,
            include_events=True
        )
        
        if history and "error" not in history:
            logger.info(f"Got session history for session {session_id}")
            logger.info(f"Session user: {history['user_identifier']}")
            logger.info(f"Session agent: {history['agent_id']}")
            
            if "messages" in history:
                logger.info(f"Found {len(history['messages'])} messages in session")
                for msg in history['messages']:
                    logger.info(f"Message: {msg['id']} - Actor: {msg['actor_type']}")
            
            if "events" in history:
                logger.info(f"Found {len(history['events'])} events in session")
                for event in history['events']:
                    logger.info(f"Event: {event['id']} - Action: {event['action_type']} - Status: {event['status']}")
        else:
            logger.error(f"Failed to get session history: {history.get('error', 'Unknown error')}")
            return False
        
        logger.info("All audit tables test passed")
        return True
    except Exception as e:
        logger.error(f"Error testing audit tables: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    if test_all_audit_tables():
        logger.info("All audit tables test passed")
        sys.exit(0)
    else:
        logger.error("All audit tables test failed")
        sys.exit(1)
