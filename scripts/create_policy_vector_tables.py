"""
<PERSON><PERSON><PERSON> to create HR policy vector tables in the database.

This script creates the necessary tables for storing HR policy vectors.
"""

import os
import sys
import logging
import psycopg2

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.env_setup import setup_environment
from utils.logging_config import configure_logging

# Configure logging
configure_logging()
logger = logging.getLogger(__name__)

# Set up environment
if not setup_environment():
    logger.error("Failed to set up environment. Please check your .env file and API keys.")
    sys.exit(1)

# Database connection parameters
DB_URL = os.environ.get("DATABASE_URL", "postgresql://rahula:postgres@localhost:5432/postgres")
SCHEMA_NAME = os.environ.get("DB_SCHEMA", "itsm")

def create_policy_vector_tables():
    """Create the HR policy vector tables in the database."""
    try:
        # Connect to the database
        conn = psycopg2.connect(DB_URL)
        conn.autocommit = True
        cursor = conn.cursor()
        
        # Create schema if it doesn't exist
        cursor.execute(f"CREATE SCHEMA IF NOT EXISTS {SCHEMA_NAME}")
        
        # Create hr_policy_vectors table if it doesn't exist
        cursor.execute(f"""
        CREATE TABLE IF NOT EXISTS {SCHEMA_NAME}.hr_policy_vectors (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            policy_id VARCHAR(50) NOT NULL,
            chunk_index INTEGER NOT NULL,
            chunk_text TEXT NOT NULL,
            embedding FLOAT[] NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            CONSTRAINT fk_policy FOREIGN KEY (policy_id) REFERENCES {SCHEMA_NAME}.hr_policies(policy_id) ON DELETE CASCADE
        )
        """)
        
        # Create index on policy_id
        cursor.execute(f"""
        CREATE INDEX IF NOT EXISTS idx_hr_policy_vectors_policy_id 
        ON {SCHEMA_NAME}.hr_policy_vectors (policy_id)
        """)
        
        logger.info("HR policy vector tables created successfully")
        
        # Close the connection
        cursor.close()
        conn.close()
        
        return True
    except Exception as e:
        logger.error(f"Error creating HR policy vector tables: {str(e)}")
        return False

if __name__ == "__main__":
    if create_policy_vector_tables():
        logger.info("HR policy vector tables created successfully")
    else:
        logger.error("Failed to create HR policy vector tables")
        sys.exit(1)
