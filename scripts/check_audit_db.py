#!/usr/bin/env python
"""
Check audit database script.

This script checks the audit database for events.
"""

import os
import sys
import logging
import datetime
from typing import Dict, Any, List, Optional

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.db_config import get_db_session, init_db
from models.audit_models import (
    AgentRegistry, AgentPrompt, AgentTool, Session,
    AuditEvent, SessionMessage
)

# Set up logger
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_audit_database():
    """Check the audit database for events."""
    # Initialize database
    init_db()

    # Get database session
    db = get_db_session()

    try:
        # Check agents
        agents = db.query(AgentRegistry).all()
        logger.info(f"Found {len(agents)} agents in the database")
        for agent in agents:
            logger.info(f"Agent: {agent.id} - {agent.name}")

        # Check sessions
        sessions = db.query(Session).all()
        logger.info(f"Found {len(sessions)} sessions in the database")
        for session in sessions:
            logger.info(f"Session: {session.id} - User: {session.user_identifier}")

        # Check events
        events = db.query(AuditEvent).all()
        logger.info(f"Found {len(events)} events in the database")
        for event in events:
            logger.info(f"Event: {event.id} - Session: {event.session_id} - Action: {event.action_type} - Status: {event.status}")

        # Check messages
        messages = db.query(SessionMessage).all()
        logger.info(f"Found {len(messages)} messages in the database")

        # Check tools
        tools = db.query(AgentTool).all()
        logger.info(f"Found {len(tools)} tools in the database")
        for tool in tools:
            logger.info(f"Tool: {tool.id} - {tool.tool_name} - Agent: {tool.agent_id}")

    finally:
        db.close()

if __name__ == "__main__":
    check_audit_database()
