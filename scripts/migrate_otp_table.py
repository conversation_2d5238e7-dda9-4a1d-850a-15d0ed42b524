#!/usr/bin/env python
"""
OTP Table Migration Script.

This script updates the otp_codes table to add new audit fields and
migrates existing records to use the new status-based approach.
"""

import os
import sys
import logging
import datetime
from sqlalchemy import Column, String, DateTime, Boolean, text

# Add parent directory to path to import project modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from utils.db_config import get_db_session, engine, Base, SCHEMA_NAME
from models.otp_models import OTPCode

# Set up logger
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def check_columns_exist():
    """Check if the new columns already exist in the table."""
    from sqlalchemy import inspect
    
    inspector = inspect(engine)
    columns = inspector.get_columns('otp_codes', schema=SCHEMA_NAME if engine.url.drivername == 'postgresql' else None)
    column_names = [col['name'] for col in columns]
    
    # Check for new columns
    new_columns = ['created_by', 'verified_at', 'last_attempt_at', 'status', 'is_latest']
    missing_columns = [col for col in new_columns if col not in column_names]
    
    return len(missing_columns) == 0


def add_new_columns():
    """Add new columns to the otp_codes table."""
    logger.info("Adding new columns to otp_codes table...")
    
    # Define SQL statements based on database type
    if engine.url.drivername == 'postgresql':
        schema_prefix = f"{SCHEMA_NAME}."
        
        # PostgreSQL statements
        statements = [
            f"ALTER TABLE {schema_prefix}otp_codes ADD COLUMN IF NOT EXISTS created_by VARCHAR(100)",
            f"ALTER TABLE {schema_prefix}otp_codes ADD COLUMN IF NOT EXISTS verified_at TIMESTAMP",
            f"ALTER TABLE {schema_prefix}otp_codes ADD COLUMN IF NOT EXISTS last_attempt_at TIMESTAMP",
            f"ALTER TABLE {schema_prefix}otp_codes ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'active'",
            f"ALTER TABLE {schema_prefix}otp_codes ADD COLUMN IF NOT EXISTS is_latest BOOLEAN DEFAULT TRUE"
        ]
    else:
        # SQLite statements
        statements = [
            "ALTER TABLE otp_codes ADD COLUMN created_by VARCHAR(100)",
            "ALTER TABLE otp_codes ADD COLUMN verified_at TIMESTAMP",
            "ALTER TABLE otp_codes ADD COLUMN last_attempt_at TIMESTAMP",
            "ALTER TABLE otp_codes ADD COLUMN status VARCHAR(20) DEFAULT 'active'",
            "ALTER TABLE otp_codes ADD COLUMN is_latest BOOLEAN DEFAULT 1"
        ]
    
    # Execute statements
    with engine.connect() as connection:
        for stmt in statements:
            try:
                connection.execute(text(stmt))
                logger.info(f"Executed: {stmt}")
            except Exception as e:
                logger.warning(f"Error executing {stmt}: {e}")
        
        connection.commit()
    
    logger.info("New columns added successfully")


def migrate_existing_records():
    """Migrate existing OTP records to use the new status field."""
    logger.info("Migrating existing OTP records...")
    
    db = get_db_session()
    try:
        # Get all OTP records
        otp_records = db.query(OTPCode).all()
        logger.info(f"Found {len(otp_records)} OTP records to migrate")
        
        # Update each record
        for record in otp_records:
            # Set created_by
            if not record.created_by:
                record.created_by = "system"
            
            # Set status based on existing fields
            if record.verified:
                record.status = "verified"
                if not record.verified_at:
                    record.verified_at = datetime.datetime.utcnow()
            elif record.is_expired():
                record.status = "expired"
            else:
                record.status = "active"
            
            # Set is_latest based on being the most recent active OTP for this user
            # (This is a simplification - in a real migration you might need more complex logic)
            record.is_latest = (record.status == "active")
            
            # Set last_attempt_at if attempts > 0
            if record.attempts > 0 and not record.last_attempt_at:
                record.last_attempt_at = datetime.datetime.utcnow()
        
        # Commit changes
        db.commit()
        logger.info("Migration completed successfully")
    except Exception as e:
        db.rollback()
        logger.error(f"Error migrating records: {e}")
        raise
    finally:
        db.close()


def main():
    """Main migration function."""
    logger.info("Starting OTP table migration")
    
    # Check if columns already exist
    if check_columns_exist():
        logger.info("New columns already exist, skipping column creation")
    else:
        # Add new columns
        add_new_columns()
    
    # Migrate existing records
    migrate_existing_records()
    
    logger.info("OTP table migration completed successfully")


if __name__ == "__main__":
    main()
