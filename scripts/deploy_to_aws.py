#!/usr/bin/env python3
"""
AWS Deployment Script for HR Solution.

This script helps deploy the HR Solution to AWS.
"""

import argparse
import logging
import os
import subprocess
import sys
from typing import Dict, Any, Optional, List

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Try to import boto3
try:
    import boto3
    from botocore.exceptions import ClientError
    AWS_AVAILABLE = True
except ImportError:
    logger.warning("AWS SDK (boto3) not installed. Installing it now...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "boto3"])
        import boto3
        from botocore.exceptions import ClientError
        AWS_AVAILABLE = True
        logger.info("Successfully installed boto3")
    except Exception as e:
        logger.error(f"Failed to install boto3: {str(e)}")
        AWS_AVAILABLE = False


def check_aws_credentials() -> bool:
    """Check if AWS credentials are configured.

    Returns:
        True if AWS credentials are configured, False otherwise.
    """
    if not AWS_AVAILABLE:
        logger.error("AWS SDK (boto3) not installed. Cannot check credentials.")
        return False

    try:
        # Try to get caller identity
        sts = boto3.client('sts')
        sts.get_caller_identity()
        return True
    except Exception as e:
        logger.error(f"AWS credentials not configured or invalid: {str(e)}")
        return False


def create_ecr_repository(repository_name: str, region: str) -> Optional[str]:
    """Create an ECR repository if it doesn't exist.

    Args:
        repository_name: The name of the repository.
        region: The AWS region.

    Returns:
        The repository URI if successful, None otherwise.
    """
    if not AWS_AVAILABLE:
        logger.error("AWS SDK (boto3) not installed. Cannot create ECR repository.")
        return None

    try:
        ecr = boto3.client('ecr', region_name=region)
        
        # Check if repository exists
        try:
            response = ecr.describe_repositories(repositoryNames=[repository_name])
            repository_uri = response['repositories'][0]['repositoryUri']
            logger.info(f"Repository {repository_name} already exists: {repository_uri}")
            return repository_uri
        except ClientError as e:
            if e.response['Error']['Code'] == 'RepositoryNotFoundException':
                # Create repository
                response = ecr.create_repository(repositoryName=repository_name)
                repository_uri = response['repository']['repositoryUri']
                logger.info(f"Created repository {repository_name}: {repository_uri}")
                return repository_uri
            else:
                logger.error(f"Error checking repository: {str(e)}")
                return None
    except Exception as e:
        logger.error(f"Failed to create ECR repository: {str(e)}")
        return None


def build_and_push_docker_image(repository_uri: str, tag: str = "latest") -> bool:
    """Build and push Docker image to ECR.

    Args:
        repository_uri: The URI of the ECR repository.
        tag: The tag to use for the image.

    Returns:
        True if successful, False otherwise.
    """
    try:
        # Build Docker image
        logger.info(f"Building Docker image...")
        subprocess.check_call(["docker", "build", "-t", f"{repository_uri}:{tag}", "."])
        
        # Get AWS account ID and region from repository URI
        # Format: {account_id}.dkr.ecr.{region}.amazonaws.com/{repository_name}
        account_id = repository_uri.split('.')[0]
        region = repository_uri.split('.')[3]
        
        # Log in to ECR
        logger.info(f"Logging in to ECR...")
        login_command = f"aws ecr get-login-password --region {region} | docker login --username AWS --password-stdin {account_id}.dkr.ecr.{region}.amazonaws.com"
        subprocess.check_call(login_command, shell=True)
        
        # Push Docker image
        logger.info(f"Pushing Docker image to {repository_uri}:{tag}...")
        subprocess.check_call(["docker", "push", f"{repository_uri}:{tag}"])
        
        logger.info(f"Successfully pushed Docker image to {repository_uri}:{tag}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Command failed: {e.cmd}, return code: {e.returncode}")
        return False
    except Exception as e:
        logger.error(f"Failed to build and push Docker image: {str(e)}")
        return False


def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Deploy HR Solution to AWS")
    parser.add_argument("--region", default="us-east-1", help="AWS region")
    parser.add_argument("--repository-name", default="hr-solution", help="ECR repository name")
    parser.add_argument("--tag", default="latest", help="Docker image tag")
    args = parser.parse_args()
    
    # Check AWS credentials
    if not check_aws_credentials():
        logger.error("AWS credentials not configured. Please run 'aws configure' first.")
        sys.exit(1)
    
    # Create ECR repository
    repository_uri = create_ecr_repository(args.repository_name, args.region)
    if not repository_uri:
        logger.error("Failed to create ECR repository.")
        sys.exit(1)
    
    # Build and push Docker image
    if not build_and_push_docker_image(repository_uri, args.tag):
        logger.error("Failed to build and push Docker image.")
        sys.exit(1)
    
    logger.info(f"Successfully deployed HR Solution to AWS ECR: {repository_uri}:{args.tag}")
    logger.info("Next steps:")
    logger.info("1. Create an ECS cluster if you don't have one")
    logger.info("2. Create a task definition using the image")
    logger.info("3. Create a service to run the task")
    logger.info("See DEPLOYMENT.md for detailed instructions")


if __name__ == "__main__":
    main()
