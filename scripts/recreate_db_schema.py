#!/usr/bin/env python
"""
Recreate database schema script.

This script drops and recreates the database schema.
"""

import os
import sys
import logging
import traceback
from typing import Dict, Any, Optional

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Set up logger
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def recreate_db_schema():
    """Drop and recreate the database schema."""
    try:
        # Import database configuration
        from utils.db_config import DB_URL, engine, SCHEMA_NAME
        
        logger.info(f"Database URL: {DB_URL}")
        
        # Check if using PostgreSQL
        if not DB_URL.startswith("postgresql"):
            logger.error("Not using PostgreSQL. Please update DB_URL in utils/db_config.py")
            return False
        
        # Try to connect to PostgreSQL
        try:
            from sqlalchemy import text
            with engine.connect() as connection:
                # Drop schema if exists
                logger.info(f"Dropping schema '{SCHEMA_NAME}' if it exists")
                connection.execute(text(f"DROP SCHEMA IF EXISTS {SCHEMA_NAME} CASCADE"))
                connection.commit()
                logger.info(f"Schema '{SCHEMA_NAME}' dropped successfully")
                
                # Create schema
                logger.info(f"Creating schema '{SCHEMA_NAME}'")
                connection.execute(text(f"CREATE SCHEMA {SCHEMA_NAME}"))
                connection.commit()
                logger.info(f"Schema '{SCHEMA_NAME}' created successfully")
                
                # Set search path
                logger.info(f"Setting search path to '{SCHEMA_NAME}'")
                connection.execute(text(f"SET search_path TO {SCHEMA_NAME}, public"))
                connection.commit()
                logger.info(f"Search path set successfully")
                
                # Create tables
                logger.info("Creating database tables")
                from utils.db_config import Base
                Base.metadata.create_all(bind=engine)
                logger.info("Database tables created successfully")
                
                # Log the tables that were created
                from sqlalchemy import inspect
                inspector = inspect(engine)
                tables = inspector.get_table_names(schema=SCHEMA_NAME)
                logger.info(f"Tables in schema '{SCHEMA_NAME}': {tables}")
                
                return True
        except Exception as e:
            logger.error(f"Error connecting to PostgreSQL database: {str(e)}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            return False
    except Exception as e:
        logger.error(f"Error recreating database schema: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    if recreate_db_schema():
        logger.info("Database schema recreated successfully")
        sys.exit(0)
    else:
        logger.error("Failed to recreate database schema")
        sys.exit(1)
