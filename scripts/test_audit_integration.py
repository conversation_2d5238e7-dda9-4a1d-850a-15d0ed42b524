#!/usr/bin/env python
"""
Test audit integration script.

This script tests the integration of audit callbacks with the existing callback system.
"""

import os
import sys
import logging
import uuid
import time
from typing import Dict, Any, Optional

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Set up logger
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_audit_integration():
    """Test the integration of audit callbacks with the existing callback system."""
    try:
        # Import necessary modules
        from utils.callbacks import register_audit_callbacks
        from utils.audit_setup import setup_agent_audit
        from services.audit_service import AuditService
        from utils.db_config import init_db
        from google.adk.agents import Agent
        from google.adk.models.llm_request import LlmRequest
        from google.adk.models.llm_response import LlmResponse
        from google.adk.agents.callback_context import CallbackContext
        from google.genai import types
        
        # Initialize database
        logger.info("Initializing database")
        init_db()
        
        # Create a test agent
        logger.info("Creating test agent")
        test_agent = Agent(
            name="test_integration_agent",
            model="test-model",
            description="Test agent for audit integration",
            instruction="You are a test agent for audit integration"
        )
        
        # Set up audit trail for the agent
        logger.info("Setting up audit trail for test agent")
        setup_agent_audit(test_agent)
        
        # Create a test session ID
        session_id = str(uuid.uuid4())
        user_id = "<EMAIL>"
        
        # Create a test callback context
        callback_context = CallbackContext(
            agent_name=test_agent.name,
            state={
                "session_id": session_id,
                "user_id": user_id
            }
        )
        
        # Create a test LLM request
        llm_request = LlmRequest(
            contents=[
                types.Content(
                    role="user",
                    parts=[types.Part(text="This is a test message")]
                )
            ]
        )
        
        # Create a test LLM response
        llm_response = LlmResponse(
            content=types.Content(
                role="model",
                parts=[types.Part(text="This is a test response")]
            )
        )
        
        # Import the combined callback
        from utils.callbacks import combined_post_processing_callback, input_safety_guardrail
        
        # Test the input safety guardrail with audit integration
        logger.info("Testing input safety guardrail with audit integration")
        result = input_safety_guardrail(callback_context, llm_request)
        logger.info(f"Input safety guardrail result: {result}")
        
        # Test the combined callback with audit integration
        logger.info("Testing combined callback with audit integration")
        result = combined_post_processing_callback(callback_context, llm_response)
        logger.info(f"Combined callback result: {result}")
        
        # Check if audit events were recorded
        logger.info("Checking if audit events were recorded")
        events = AuditService.get_audit_events(session_id=session_id)
        
        if events:
            logger.info(f"Found {len(events)} audit events for session {session_id}")
            for event in events:
                logger.info(f"Event: {event['id']} - Action: {event['action_type']} - Status: {event['status']}")
                logger.info(f"  Context data: {event['context_data']}")
        else:
            logger.error(f"No audit events found for session {session_id}")
            return False
        
        # Check if session messages were recorded
        logger.info("Checking if session messages were recorded")
        messages = AuditService.get_session_messages(session_id=session_id)
        
        if messages:
            logger.info(f"Found {len(messages)} session messages for session {session_id}")
            for message in messages:
                logger.info(f"Message: {message['id']} - Actor: {message['actor_type']} - Message: {message['message']}")
        else:
            logger.error(f"No session messages found for session {session_id}")
            return False
        
        logger.info("Audit integration test passed")
        return True
    except Exception as e:
        logger.error(f"Error testing audit integration: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    if test_audit_integration():
        logger.info("Audit integration test passed")
        sys.exit(0)
    else:
        logger.error("Audit integration test failed")
        sys.exit(1)
