#!/usr/bin/env python
"""
Test password reset with audit trail.

This script tests the password reset tool with audit trail.
"""

import os
import sys
import logging
import uuid
import json
import time
import random
import string
from typing import Dict, Any

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Mock the ToolContext class
class ToolContext:
    def __init__(self, agent_name, state=None):
        self.agent_name = agent_name
        self.state = state or {}

# Import the database modules
from utils.db_config import get_db_session, init_db
from models.audit_models import AuditEvent, Session, AgentRegistry
from utils.audit_callbacks import record_tool_result

# Set up logger
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_password_reset_audit():
    """Test password reset with audit trail."""
    # Initialize database
    init_db()

    # Create a session ID
    session_id = str(uuid.uuid4())
    user_id = "<EMAIL>"

    # Create a tool context
    tool_context = ToolContext(
        agent_name="ITSM Agent",
        state={
            "session_id": session_id,
            "user_id": user_id
        }
    )

    # Ensure agent exists in database
    db = get_db_session()
    try:
        # Check if agent exists
        agent = db.query(AgentRegistry).filter(AgentRegistry.name == "ITSM Agent").first()
        if not agent:
            # Create agent
            agent = AgentRegistry(
                name="ITSM Agent",
                description="IT Service Management Agent",
                is_active=True
            )
            db.add(agent)
            db.commit()
            logger.info(f"Created agent with ID {agent.id}")
        else:
            logger.info(f"Found existing agent with ID {agent.id}")

        # Create session
        session = Session(
            id=session_id,
            user_identifier=user_id,
            agent_id=agent.id
        )
        db.add(session)
        db.commit()
        logger.info(f"Created session with ID {session.id}")
    except Exception as e:
        logger.error(f"Error setting up database: {str(e)}")
        db.rollback()
        raise
    finally:
        db.close()

    # Generate a mock password reset result
    # Generate a random temporary password
    lowercase = ''.join(random.choices(string.ascii_lowercase, k=4))
    uppercase = ''.join(random.choices(string.ascii_uppercase, k=4))
    digits = ''.join(random.choices(string.digits, k=3))
    special = ''.join(random.choices('!@#$%^&*()-_=+[]{}|;:,.<>?', k=3))
    all_chars = list(lowercase + uppercase + digits + special)
    random.shuffle(all_chars)
    temp_password = ''.join(all_chars)

    # Create mock result
    result = {
        "status": "success",
        "username": "jdoe",
        "email": "<EMAIL>",
        "temp_password": temp_password,
        "message": "Password for jdoe has been reset. A temporary password has been generated.",
        "notification": "✅ Password Reset Successful!",
        "account_unlocked": False,
        "is_mock": True,
        "note": "This password was reset using the mock implementation."
    }

    # Record the audit event
    logger.info("Recording audit event for password reset")
    record_tool_result(
        tool_name="reset_password",
        agent_name="ITSM Agent",
        session_id=session_id,
        user_id=user_id,
        args={"user_identifier": "<EMAIL>", "requesting_user_email": user_id, "is_admin": True},
        result=result,
        status="success",
        entity_type="password",
        entity_id="jdoe"
    )

    # Check audit events
    db = get_db_session()
    try:
        # Query audit events for this session
        events = db.query(AuditEvent).filter(AuditEvent.session_id == session_id).all()
        logger.info(f"Found {len(events)} audit events for this session")

        for event in events:
            logger.info(f"Event: {event.id} - Action: {event.action_type} - Status: {event.status}")
            if event.context_data:
                logger.info(f"  Context data: {event.context_data}")
    finally:
        db.close()

if __name__ == "__main__":
    test_password_reset_audit()
