"""
<PERSON><PERSON><PERSON> to vectorize HR policies.

This script retrieves all HR policies from the database and generates vector embeddings for them.
"""

import os
import sys
import logging
import time

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.env_setup import setup_environment
from utils.logging_config import configure_logging
from services.policy_vector_service import PolicyVectorService

# Configure logging
configure_logging()
logger = logging.getLogger(__name__)

# Set up environment
if not setup_environment():
    logger.error("Failed to set up environment. Please check your .env file and API keys.")
    sys.exit(1)

def vectorize_policies():
    """Vectorize all HR policies in the database."""
    try:
        # Create policy vector service
        service = PolicyVectorService()
        
        # First, ensure the vector tables exist
        from scripts.create_policy_vector_tables import create_policy_vector_tables
        if not create_policy_vector_tables():
            logger.error("Failed to create policy vector tables")
            return False
        
        # Get all policies
        logger.info("Retrieving HR policies from database")
        policies = service.get_all_policies()
        logger.info(f"Retrieved {len(policies)} HR policies")
        
        if not policies:
            logger.warning("No HR policies found in the database")
            return False
        
        # Vectorize policies
        logger.info("Vectorizing HR policies")
        start_time = time.time()
        count = service.vectorize_all_policies()
        end_time = time.time()
        
        logger.info(f"Vectorized {count} HR policies in {end_time - start_time:.2f} seconds")
        
        return count > 0
    except Exception as e:
        logger.error(f"Error vectorizing HR policies: {str(e)}")
        return False

if __name__ == "__main__":
    if vectorize_policies():
        logger.info("HR policies vectorized successfully")
    else:
        logger.error("Failed to vectorize HR policies")
        sys.exit(1)
