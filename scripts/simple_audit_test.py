#!/usr/bin/env python
"""
Simple audit test script.

This script tests the audit database by directly inserting records.
"""

import os
import sys
import logging
import uuid
import json
import datetime
from typing import Dict, Any

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.db_config import get_db_session, init_db
from models.audit_models import AgentRegistry, Session, AuditEvent

# Set up logger
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_audit_database():
    """Test the audit database by directly inserting records."""
    # Initialize database
    init_db()
    
    # Create a session ID
    session_id = str(uuid.uuid4())
    user_id = "<EMAIL>"
    
    # Get database session
    db = get_db_session()
    try:
        # Check if agent exists
        agent = db.query(AgentRegistry).filter(AgentRegistry.name == "ITSM Agent").first()
        if not agent:
            # Create agent
            agent = AgentRegistry(
                name="ITSM Agent",
                description="IT Service Management Agent",
                is_active=True
            )
            db.add(agent)
            db.commit()
            logger.info(f"Created agent with ID {agent.id}")
        else:
            logger.info(f"Found existing agent with ID {agent.id}")
        
        # Create session
        session = Session(
            id=session_id,
            user_identifier=user_id,
            agent_id=agent.id
        )
        db.add(session)
        db.commit()
        logger.info(f"Created session with ID {session.id}")
        
        # Create audit event
        audit_event = AuditEvent(
            session_id=session_id,
            agent_id=agent.id,
            user_identifier=user_id,
            actor_type="tool",
            actor_id="reset_password",
            action_type="tool_result",
            entity_type="password",
            entity_id="jdoe",
            status="success",
            context_data={
                "args": {
                    "user_identifier": "<EMAIL>",
                    "requesting_user_email": user_id,
                    "is_admin": True
                },
                "result": {
                    "status": "success",
                    "username": "jdoe",
                    "email": "<EMAIL>",
                    "message": "Password for jdoe has been reset. A temporary password has been generated.",
                    "notification": "✅ Password Reset Successful!"
                }
            }
        )
        db.add(audit_event)
        db.commit()
        logger.info(f"Created audit event with ID {audit_event.id}")
        
        # Query audit events for this session
        events = db.query(AuditEvent).filter(AuditEvent.session_id == session_id).all()
        logger.info(f"Found {len(events)} audit events for this session")
        
        for event in events:
            logger.info(f"Event: {event.id} - Action: {event.action_type} - Status: {event.status}")
            if event.context_data:
                logger.info(f"  Context data: {event.context_data}")
    except Exception as e:
        logger.error(f"Error: {str(e)}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    test_audit_database()
