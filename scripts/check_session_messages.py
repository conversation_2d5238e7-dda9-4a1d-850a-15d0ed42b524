#!/usr/bin/env python
"""
Check session messages script.

This script checks the session messages in the audit database.
"""

import os
import sys
import logging
import traceback
from typing import Dict, Any, Optional

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Set up logger
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_session_messages():
    """Check the session messages in the audit database."""
    try:
        # Import database configuration
        from utils.db_config import init_db
        from services.audit_service import AuditService

        # Initialize database
        logger.info("Initializing database")
        init_db()

        # Get all sessions from the database directly
        logger.info("Getting all sessions from the database")
        from utils.db_config import get_db_session
        from models.audit_models import Session, SessionMessage, AgentPrompt, AgentTool

        db = get_db_session()
        try:
            # Get all sessions
            sessions = db.query(Session).all()

            if not sessions:
                logger.error("No sessions found in the database")
                return False

            logger.info(f"Found {len(sessions)} sessions in the database")

            # Check each session for messages
            for session in sessions:
                session_id = session.id
                user_id = session.user_identifier

                logger.info(f"Checking messages for session {session_id} - User: {user_id}")

                # Get session messages
                messages = db.query(SessionMessage).filter_by(session_id=session_id).all()

                if messages:
                    logger.info(f"Found {len(messages)} messages in session {session_id}")
                    for message in messages:
                        message_text = message.message[:50] + "..." if message.message and len(message.message) > 50 else message.message
                        logger.info(f"Message: {message.id} - Actor: {message.actor_type} - Message: {message_text}")
                else:
                    logger.info(f"No messages found for session {session_id}")

            # Get all agent prompts
            logger.info("Getting all agent prompts")
            prompts = db.query(AgentPrompt).all()

            if prompts:
                logger.info(f"Found {len(prompts)} agent prompts in the database")
                for prompt in prompts:
                    logger.info(f"Prompt: {prompt.id} - Agent: {prompt.agent_id} - Type: {prompt.prompt_type}")
            else:
                logger.info("No agent prompts found in the database")

            # Get all agent tools
            logger.info("Getting all agent tools")
            tools = db.query(AgentTool).all()

            if tools:
                logger.info(f"Found {len(tools)} agent tools in the database")
                for tool in tools:
                    logger.info(f"Tool: {tool.id} - Name: {tool.tool_name} - Agent: {tool.agent_id}")
            else:
                logger.info("No agent tools found in the database")

        return True
    except Exception as e:
        logger.error(f"Error checking session messages: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    if check_session_messages():
        logger.info("Session messages check passed")
        sys.exit(0)
    else:
        logger.error("Session messages check failed")
        sys.exit(1)
