#!/usr/bin/env python
"""
Cleanup duplicate agent prompts.

This script identifies and removes duplicate agent prompts in the database,
keeping only the most recent prompt for each agent and prompt type.
"""

import logging
import sys
import os
from collections import defaultdict

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.db_config import get_db_session, init_db
from models.audit_models import AgentPrompt

# Set up logger
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def cleanup_duplicate_prompts():
    """Clean up duplicate agent prompts in the database."""
    logger.info("Starting cleanup of duplicate agent prompts")

    # Initialize database
    init_db()

    # Get database session
    db = get_db_session()
    try:
        # Get all agent prompts
        prompts = db.query(AgentPrompt).order_by(AgentPrompt.created_at.desc()).all()
        
        if not prompts:
            logger.info("No agent prompts found in the database")
            return
        
        logger.info(f"Found {len(prompts)} agent prompts in the database")
        
        # Group prompts by agent_id and prompt_type
        prompt_groups = defaultdict(list)
        for prompt in prompts:
            key = (prompt.agent_id, prompt.prompt_type)
            prompt_groups[key].append(prompt)
        
        # Count total duplicates
        total_duplicates = sum(len(group) - 1 for group in prompt_groups.values() if len(group) > 1)
        logger.info(f"Found {total_duplicates} duplicate prompts")
        
        # For each group, keep the most recent prompt and delete the rest
        for key, group in prompt_groups.items():
            if len(group) > 1:
                agent_id, prompt_type = key
                logger.info(f"Found {len(group)} prompts for agent {agent_id} with type '{prompt_type}'")
                
                # Keep the most recent prompt (first in the list since we ordered by created_at desc)
                keep_prompt = group[0]
                logger.info(f"Keeping prompt {keep_prompt.id} (created at {keep_prompt.created_at})")
                
                # Delete the rest
                for prompt in group[1:]:
                    logger.info(f"Deleting duplicate prompt {prompt.id} (created at {prompt.created_at})")
                    db.delete(prompt)
        
        # Commit changes
        db.commit()
        logger.info(f"Successfully deleted {total_duplicates} duplicate prompts")
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error cleaning up duplicate prompts: {str(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
    finally:
        db.close()


if __name__ == "__main__":
    cleanup_duplicate_prompts()
