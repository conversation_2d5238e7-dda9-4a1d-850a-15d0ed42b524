#!/usr/bin/env python
"""
Check PostgreSQL user permissions script.

This script checks if the PostgreSQL user has the necessary permissions.
"""

import os
import sys
import logging
import traceback
from typing import Dict, Any, Optional

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Set up logger
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_postgres_user():
    """Check if the PostgreSQL user has the necessary permissions."""
    try:
        # Import database configuration
        from utils.db_config import DB_URL, engine, SCHEMA_NAME
        
        logger.info(f"Database URL: {DB_URL}")
        
        # Check if using PostgreSQL
        if not DB_URL.startswith("postgresql"):
            logger.error("Not using PostgreSQL. Please update DB_URL in utils/db_config.py")
            return False
        
        # Extract username from DB_URL
        import re
        username_match = re.search(r"postgresql://([^:]+):", DB_URL)
        if username_match:
            username = username_match.group(1)
            logger.info(f"PostgreSQL username: {username}")
        else:
            logger.error(f"Could not extract username from DB_URL: {DB_URL}")
            return False
        
        # Try to connect to PostgreSQL
        try:
            from sqlalchemy import text
            with engine.connect() as connection:
                # Check if user exists
                logger.info(f"Checking if user '{username}' exists")
                result = connection.execute(text(f"SELECT 1 FROM pg_roles WHERE rolname = '{username}'"))
                user_exists = result.fetchone() is not None
                
                if user_exists:
                    logger.info(f"User '{username}' exists")
                else:
                    logger.error(f"User '{username}' does not exist")
                    logger.error(f"Please create the user with: CREATE ROLE {username} WITH LOGIN PASSWORD 'your_password';")
                    return False
                
                # Check if user has necessary permissions
                logger.info(f"Checking if user '{username}' has necessary permissions")
                
                # Check if user can create schema
                try:
                    logger.info(f"Checking if user '{username}' can create schema")
                    connection.execute(text(f"CREATE SCHEMA IF NOT EXISTS {SCHEMA_NAME}_test"))
                    connection.commit()
                    logger.info(f"User '{username}' can create schema")
                    
                    # Drop test schema
                    connection.execute(text(f"DROP SCHEMA {SCHEMA_NAME}_test"))
                    connection.commit()
                    logger.info(f"Dropped test schema '{SCHEMA_NAME}_test'")
                except Exception as e:
                    logger.error(f"User '{username}' cannot create schema: {str(e)}")
                    logger.error(f"Please grant necessary permissions with: GRANT CREATE ON DATABASE postgres TO {username};")
                    return False
                
                # Check if user can create tables
                try:
                    logger.info(f"Checking if user '{username}' can create tables")
                    connection.execute(text(f"CREATE SCHEMA IF NOT EXISTS {SCHEMA_NAME}"))
                    connection.commit()
                    connection.execute(text(f"""
                        CREATE TABLE IF NOT EXISTS {SCHEMA_NAME}.test_table (
                            id SERIAL PRIMARY KEY,
                            name TEXT
                        )
                    """))
                    connection.commit()
                    logger.info(f"User '{username}' can create tables")
                    
                    # Drop test table
                    connection.execute(text(f"DROP TABLE {SCHEMA_NAME}.test_table"))
                    connection.commit()
                    logger.info(f"Dropped test table '{SCHEMA_NAME}.test_table'")
                except Exception as e:
                    logger.error(f"User '{username}' cannot create tables: {str(e)}")
                    logger.error(f"Please grant necessary permissions with: GRANT ALL PRIVILEGES ON SCHEMA {SCHEMA_NAME} TO {username};")
                    return False
                
                logger.info(f"User '{username}' has all necessary permissions")
                return True
        except Exception as e:
            logger.error(f"Error connecting to PostgreSQL database: {str(e)}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            return False
    except Exception as e:
        logger.error(f"Error checking database configuration: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    if check_postgres_user():
        logger.info("PostgreSQL user check passed")
        sys.exit(0)
    else:
        logger.error("PostgreSQL user check failed")
        sys.exit(1)
