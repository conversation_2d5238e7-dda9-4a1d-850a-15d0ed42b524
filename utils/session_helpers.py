"""
Session state helper utilities.

This module provides helper functions for working with session state
across agents to ensure proper data sharing.
"""

import logging
from typing import Any, Optional

# Set up logger
logger = logging.getLogger(__name__)

def set_session_value(session, key: str, value: Any) -> None:
    """Store a value in the session state.
    
    Args:
        session: The current session object
        key: The key to store the value under
        value: The value to store
    """
    try:
        session.state[key] = value
        logger.info(f"Stored value in session state: {key}")
    except Exception as e:
        logger.error(f"Failed to store value in session state: {e}")

def get_session_value(session, key: str, default: Any = None) -> Any:
    """Retrieve a value from the session state.
    
    Args:
        session: The current session object
        key: The key to retrieve
        default: Default value to return if key doesn't exist
        
    Returns:
        The stored value or the default if not found
    """
    try:
        value = session.state.get(key, default)
        if value != default:
            logger.info(f"Retrieved value from session state: {key}")
        return value
    except Exception as e:
        logger.error(f"Failed to retrieve value from session state: {e}")
        return default

def clear_session_value(session, key: str) -> None:
    """Remove a value from the session state.
    
    Args:
        session: The current session object
        key: The key to remove
    """
    try:
        if key in session.state:
            del session.state[key]
            logger.info(f"Cleared value from session state: {key}")
    except Exception as e:
        logger.error(f"Failed to clear value from session state: {e}") 