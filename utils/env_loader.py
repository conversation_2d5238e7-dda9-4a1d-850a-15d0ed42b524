"""
Environment variable loader.

This module provides utilities for loading environment variables
from a .env file for configuration.
"""

import os
import logging
from pathlib import Path
from typing import Dict, Optional, Any

# Set up logger
logger = logging.getLogger(__name__)

def load_env_file(env_file: str = ".env") -> Dict[str, str]:
    """Load environment variables from a .env file.
    
    Args:
        env_file: Path to the .env file
        
    Returns:
        Dictionary of environment variables
    """
    env_vars = {}
    
    # Check if the file exists
    env_path = Path(env_file)
    if not env_path.exists():
        logger.warning(f".env file not found at {env_path.absolute()}")
        return env_vars
    
    try:
        # Read the file
        with open(env_path, "r") as f:
            for line in f:
                line = line.strip()
                
                # Skip empty lines and comments
                if not line or line.startswith("#"):
                    continue
                
                # Parse key-value pairs
                if "=" in line:
                    key, value = line.split("=", 1)
                    key = key.strip()
                    value = value.strip()
                    
                    # Remove quotes if present
                    if value.startswith('"') and value.endswith('"'):
                        value = value[1:-1]
                    elif value.startswith("'") and value.endswith("'"):
                        value = value[1:-1]
                    
                    env_vars[key] = value
        
        logger.info(f"Loaded {len(env_vars)} environment variables from {env_file}")
    except Exception as e:
        logger.error(f"Error loading .env file: {str(e)}")
    
    return env_vars

def set_env_vars(env_vars: Dict[str, str], overwrite: bool = False) -> None:
    """Set environment variables from a dictionary.
    
    Args:
        env_vars: Dictionary of environment variables
        overwrite: Whether to overwrite existing environment variables
    """
    for key, value in env_vars.items():
        if key not in os.environ or overwrite:
            os.environ[key] = value

def get_env_var(key: str, default: Optional[str] = None) -> Optional[str]:
    """Get an environment variable.
    
    Args:
        key: Environment variable key
        default: Default value if not found
        
    Returns:
        Environment variable value or default
    """
    return os.environ.get(key, default)

def init_env(env_file: str = ".env", overwrite: bool = False) -> None:
    """Initialize environment variables from a .env file.
    
    Args:
        env_file: Path to the .env file
        overwrite: Whether to overwrite existing environment variables
    """
    env_vars = load_env_file(env_file)
    set_env_vars(env_vars, overwrite)
    
    # Check for required variables
    required_vars = [
        "COGNITO_USER_POOL_ID",
        "COGNITO_CLIENT_ID",
        "COGNITO_REGION"
    ]
    
    missing_vars = [var for var in required_vars if not get_env_var(var)]
    if missing_vars:
        logger.warning(f"Missing required environment variables: {', '.join(missing_vars)}")
        logger.warning("Using mock credentials for third-party API authentication")

# Initialize environment variables when the module is imported
init_env()
