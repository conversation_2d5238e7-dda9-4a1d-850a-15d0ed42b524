"""
Logging configuration module.

This module contains the logging configuration for the ITSM solution.
"""

import logging
import logging.handlers
import os
import sys
from typing import Optional, Dict, Any


def configure_logging(
    log_level: int = logging.INFO,
    log_file: Optional[str] = None,
    log_format: Optional[str] = None,
    enable_console: bool = True,
    additional_handlers: Optional[Dict[str, Any]] = None
) -> logging.Logger:
    """Configure logging for the ITSM solution.

    Args:
        log_level: The logging level to use. Defaults to INFO.
        log_file: Path to the log file. If None, logging to file is disabled.
        log_format: Format string for log messages. If None, a default format is used.
        enable_console: Whether to enable console logging. Defaults to True.
        additional_handlers: Dictionary of additional handlers to add to the logger.

    Returns:
        The configured root logger.
    """
    # Create root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)

    # Clear existing handlers (useful if function is called multiple times)
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # Define default format if not provided
    if log_format is None:
        log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

    formatter = logging.Formatter(log_format)

    # Add console handler if enabled
    if enable_console:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)

    # Add file handler if log_file provided
    if log_file:
        # Ensure directory exists
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir)

        # Create rotating file handler (10 MB per file, max 5 files)
        file_handler = logging.handlers.RotatingFileHandler(
            log_file, maxBytes=10*1024*1024, backupCount=5
        )
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)

    # Add any additional handlers
    if additional_handlers:
        for handler_name, handler in additional_handlers.items():
            if isinstance(handler, logging.Handler):
                handler.setFormatter(formatter)
                root_logger.addHandler(handler)
            else:
                root_logger.warning(f"Ignoring invalid handler {handler_name}")

    # Set higher log levels for specific loggers to suppress INFO messages
    # Google ADK loggers
    logging.getLogger('google').setLevel(logging.WARNING)
    logging.getLogger('google.adk').setLevel(logging.WARNING)
    logging.getLogger('google.genai').setLevel(logging.WARNING)
    logging.getLogger('google_genai').setLevel(logging.WARNING)
    logging.getLogger('httpx').setLevel(logging.WARNING)

    # Create ITSM specific logger
    itsm_logger = logging.getLogger('itsm_solution')
    itsm_logger.info("Logging initialized")

    return root_logger


def get_logger(name: str) -> logging.Logger:
    """Get a logger with the specified name.

    Args:
        name: The name of the logger.

    Returns:
        A logger instance.
    """
    return logging.getLogger(name)