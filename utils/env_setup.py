"""
Environment setup utility.

This module provides utilities for setting up the environment for the HR solution.
"""

import os
import logging
from pathlib import Path
from typing import Optional

from dotenv import load_dotenv

logger = logging.getLogger(__name__)


def setup_environment(dotenv_path: Optional[str] = None) -> bool:
    """Set up the environment for the HR solution.

    Args:
        dotenv_path: Path to .env file. If None, looks for .env in current directory and parents.

    Returns:
        True if the environment is properly set up, False otherwise.
    """
    # Try to load from .env file if provided or exists
    if dotenv_path:
        env_loaded = load_dotenv(dotenv_path)
        if env_loaded:
            logger.info(f"Loaded environment from {dotenv_path}")
    else:
        # Try to find .env in current directory or parent directories
        env_loaded = load_dotenv()
        if env_loaded:
            logger.info("Loaded environment from .env file")
        else:
            logger.warning("No .env file found. Using environment variables from system.")

    # Set necessary environment variables for Google ADK
    # Always set GOOGLE_GENAI_USE_VERTEXAI to False for AWS deployment
    os.environ["GOOGLE_GENAI_USE_VERTEXAI"] = "False"

    # Always use InMemorySessionService
    os.environ["USE_VERTEX_AI_SESSION"] = "False"

    # Check for Google API key (required for Google ADK)
    google_api_key = os.environ.get("GOOGLE_API_KEY")
    if not google_api_key:
        logger.error("GOOGLE_API_KEY environment variable is not set.")
        return False
    logger.info("Using Google API key authentication")

    # Check for AWS configuration
    aws_region = os.environ.get("AWS_REGION")
    if not aws_region:
        logger.warning("AWS_REGION environment variable is not set. Using default: us-east-1")
        os.environ["AWS_REGION"] = "us-east-1"

    # Set log level from environment or default to INFO
    log_level = os.environ.get("LOG_LEVEL", "INFO")
    log_levels = {
        "DEBUG": logging.DEBUG,
        "INFO": logging.INFO,
        "WARNING": logging.WARNING,
        "ERROR": logging.ERROR,
        "CRITICAL": logging.CRITICAL
    }
    os.environ["LOG_LEVEL"] = log_level

    logger.info(f"Environment is set up. Log level: {log_level}, AWS Region: {os.environ.get('AWS_REGION')}")
    return True


def create_default_env_file(output_path: str = ".env.example") -> bool:
    """Create a default .env file with all required variables.

    Args:
        output_path: Path where to create the .env file.

    Returns:
        True if the file was created successfully, False otherwise.
    """
    content = """# Environment Configuration
ENVIRONMENT=dev  # dev, staging, prod

# Google API Key - Required for Google ADK
GOOGLE_API_KEY=your-google-api-key-here
GOOGLE_GENAI_USE_VERTEXAI=False

# AWS Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-access-key-id
AWS_SECRET_ACCESS_KEY=your-secret-access-key

# AWS OpenSearch Configuration
OPENSEARCH_ENDPOINT=https://your-opensearch-domain.us-east-1.es.amazonaws.com
OPENSEARCH_POLICY_INDEX=hr-policies
EMBEDDING_MODEL=all-MiniLM-L6-v2

# Database Configuration
DATABASE_URL=*****************************************************/postgres

# Model Configuration
ROOT_MODEL=gemini-2.0-flash
SUB_AGENT_MODEL=gemini-2.0-flash

# Logging Configuration
LOG_LEVEL=INFO  # DEBUG, INFO, WARNING, ERROR, CRITICAL

# LiteLLM API Keys (Optional - only needed if using OpenAI or Claude models)
# OPENAI_API_KEY=your-openai-api-key-here
# ANTHROPIC_API_KEY=your-anthropic-api-key-here
"""

    try:
        with open(output_path, "w") as f:
            f.write(content)
        logger.info(f"Created default .env file at {output_path}")
        return True
    except Exception as e:
        logger.error(f"Failed to create default .env file: {str(e)}")
        return False


if __name__ == "__main__":
    """Run as a standalone script to create a default .env file."""
    from utils.logging_config import configure_logging

    configure_logging()
    create_default_env_file()
    print("Created .env.example file in the current directory.")
    print("Rename it to .env and update with your actual API keys.")