"""
HR API Client.

This module provides clients for interacting with HR APIs
that are protected by Microsoft tokens from frontend and WagonHR authentication.
"""

import os
import json
import logging
from typing import Dict, List, Optional, Any, Tuple

from utils.hr_auth import hr_auth

# Set up logger
logger = logging.getLogger(__name__)

# Default WagonHR API base URL
DEFAULT_WAGONHR_API_URL = "https://dev-api-wagonhr.mouritech.net"

class HRAPIClient:
    """Base client for HR API interactions."""

    def __init__(self, base_url: str = None, auth_type: str = "wagonhr"):
        """Initialize the HR API client.

        Args:
            base_url: Base URL for the API
            auth_type: Authentication type to use ('microsoft' or 'wagonhr')
        """
        self.base_url = base_url or os.environ.get("HR_API_BASE_URL", DEFAULT_WAGONHR_API_URL)
        self.auth = hr_auth
        self.auth_type = auth_type

        # Default headers for tenant/entity context
        self.default_headers = {}

        # Set default tenant/entity headers from environment if available
        if os.environ.get("HR_TENANT_ID"):
            self.default_headers["tenantId"] = os.environ.get("HR_TENANT_ID")
        if os.environ.get("HR_ENTITY_ID"):
            self.default_headers["entityId"] = os.environ.get("HR_ENTITY_ID")
        if os.environ.get("HR_ORGANIZATION_ID"):
            self.default_headers["organizationId"] = os.environ.get("HR_ORGANIZATION_ID")

        # Ensure authentication is initialized
        if self.auth_type.lower() == "microsoft":
            if not self.auth.ms_access_token:
                self.auth.authenticate_microsoft()
        else:  # Default to WagonHR
            if not self.auth.wagonhr_access_token:
                self.auth.authenticate_wagonhr()

    def set_tenant_context(self, tenant_id: str, entity_id: str = None, organization_id: str = None):
        """Set tenant/entity context for API requests.

        Args:
            tenant_id: Tenant ID for multi-tenant APIs
            entity_id: Entity ID for multi-entity APIs
            organization_id: Organization ID for multi-org APIs
        """
        self.default_headers["x-tenantid"] = tenant_id
        if entity_id:
            self.default_headers["x-entityid"] = entity_id
        if organization_id:
            self.default_headers["organizationId"] = organization_id
        logger.info(f"Set tenant context: tenantId={tenant_id}, entityId={entity_id}, organizationId={organization_id}")

    def _merge_headers(self, additional_headers: Optional[Dict[str, str]] = None, include_auth: bool = False) -> Dict[str, str]:
        """Merge default headers with additional headers and optionally include auth headers.

        Args:
            additional_headers: Optional additional headers
            include_auth: Whether to include authentication headers

        Returns:
            Merged headers dictionary
        """
        merged_headers = self.default_headers.copy()

        # Add authentication headers if requested
        if include_auth:
            if self.auth_type.lower() == "microsoft":
                auth_headers = self.auth.get_ms_auth_headers()
            else:  # Default to WagonHR
                auth_headers = self.auth.get_wagonhr_auth_headers()

            if auth_headers:
                merged_headers.update(auth_headers)

        # Add additional headers (these can override auth headers if needed)
        if additional_headers:
            merged_headers.update(additional_headers)

        return merged_headers

    def get_all_headers(self, additional_headers: Optional[Dict[str, str]] = None) -> Dict[str, str]:
        """Get all headers including authentication headers for inspection.

        Args:
            additional_headers: Optional additional headers

        Returns:
            Complete headers dictionary including authentication
        """
        return self._merge_headers(additional_headers, include_auth=True)

    def _build_url(self, endpoint: str) -> str:
        """Build a full URL from the endpoint.

        Args:
            endpoint: API endpoint path

        Returns:
            Full URL
        """
        # Ensure the base URL doesn't end with a slash and the endpoint starts with one
        base = self.base_url.rstrip("/")
        path = endpoint.lstrip("/")
        return f"{base}/{path}"

    def get(
        self,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None
    ) -> Tuple[int, Dict[str, Any]]:
        """Make a GET request to the API.

        Args:
            endpoint: API endpoint path
            params: Optional query parameters
            headers: Optional additional headers

        Returns:
            Tuple of (status_code, response_data)
        """
        url = self._build_url(endpoint)
        merged_headers = self._merge_headers(headers, include_auth=True)

        # Log the request with detailed header info
        header_keys = list(merged_headers.keys())
        logger.info(f"=== HTTP GET REQUEST ===")
        logger.info(f"URL: {url}")
        logger.info(f"Endpoint: {endpoint}")
        logger.info(f"Query Params: {params}")
        logger.info(f"Header Keys: {header_keys}")
        logger.info(f"Auth Type: {self.auth_type}")

        # Check for specific auth headers
        if 'Authorization' in merged_headers:
            auth_value = merged_headers['Authorization']
            logger.info(f"Authorization Header: {auth_value[:20]}..." if len(auth_value) > 20 else f"Authorization Header: {auth_value}")
        else:
            logger.warning("NO AUTHORIZATION HEADER FOUND!")

        if 'x-tenantid' in merged_headers:
            logger.info(f"Tenant ID: {merged_headers['x-tenantid']}")
        if 'x-entityid' in merged_headers:
            logger.info(f"Entity ID: {merged_headers['x-entityid']}")

        result = self.auth.make_authenticated_request(
            method="GET",
            url=url,
            auth_type=self.auth_type,
            params=params,
            headers=merged_headers
        )

        logger.info(f"HTTP Response Status: {result[0]}")
        logger.info(f"=== HTTP GET REQUEST COMPLETE ===")

        return result

    def post(
        self,
        endpoint: str,
        data: Dict[str, Any],
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None
    ) -> Tuple[int, Dict[str, Any]]:
        """Make a POST request to the API.

        Args:
            endpoint: API endpoint path
            data: Request body data
            params: Optional query parameters
            headers: Optional additional headers

        Returns:
            Tuple of (status_code, response_data)
        """
        url = self._build_url(endpoint)
        merged_headers = self._merge_headers(headers)

        # Log the request with header info (excluding sensitive auth token values)
        header_keys = list(merged_headers.keys())
        logger.info(f"Making POST request to {url} with headers: {header_keys}")

        return self.auth.make_authenticated_request(
            method="POST",
            url=url,
            auth_type=self.auth_type,
            data=data,
            params=params,
            headers=merged_headers
        )

    def handle_response(
        self,
        status_code: int,
        response_data: Dict[str, Any],
        success_message: str,
        error_message: str
    ) -> Dict[str, Any]:
        """Handle API response and format it consistently.

        Args:
            status_code: HTTP status code
            response_data: Response data from the API
            success_message: Message to include on success
            error_message: Message to include on error

        Returns:
            Formatted response dictionary
        """
        if 200 <= status_code < 300:
            return {
                "status": "success",
                "message": success_message,
                "data": response_data
            }
        else:
            error = response_data.get("error", "Unknown error")
            logger.error(f"{error_message}: {error}")
            return {
                "status": "error",
                "message": f"{error_message}: {error}",
                "error_code": status_code
            }

class LeaveBalanceClient(HRAPIClient):
    """Client for Leave Balance API."""

    def __init__(self):
        """Initialize the Leave Balance API client."""
        super().__init__(
            os.environ.get("LEAVE_API_BASE_URL", DEFAULT_WAGONHR_API_URL),
            auth_type="wagonhr"
        )

    def get_leave_balance(
        self,
        employee_id: Optional[str] = None,
        email: Optional[str] = None,
        headers: Optional[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """Get leave balance for an employee.

        Args:
            employee_id: Optional employee ID
            email: Optional employee email
            headers: Optional additional headers (e.g., tenantId, entityId)

        Returns:
            Leave balance information
        """
        logger.info("=== LEAVE BALANCE CLIENT API CALL ===")
        logger.info("Client: LeaveBalanceClient")
        logger.info("Method: get_leave_balance()")
        logger.info(f"Base URL: {self.base_url}")
        logger.info(f"Auth Type: {self.auth_type}")

        params = {}
        if employee_id:
            params["employeeId"] = employee_id
        if email:
            params["email"] = email

        endpoint = "/api/hrms/leave-attendance/employee-leaves"
        full_url = self._build_url(endpoint)

        logger.info(f"API Endpoint: {endpoint}")
        logger.info(f"Full URL: {full_url}")
        logger.info(f"Query Parameters: {params}")
        logger.info(f"Additional Headers: {headers}")

        # Get all headers including auth for logging
        all_headers = self.get_all_headers(headers)
        logger.info(f"All Headers (keys only): {list(all_headers.keys())}")

        # Check if we have auth token
        if 'Authorization' in all_headers:
            logger.info("Authorization header present")
        else:
            logger.warning("Authorization header MISSING")

        status_code, response_data = self.get(endpoint, params=params, headers=headers)

        logger.info(f"HTTP Status Code: {status_code}")
        logger.info(f"Response Data Keys: {list(response_data.keys()) if isinstance(response_data, dict) else 'Not a dict'}")

        result = self.handle_response(
            status_code,
            response_data,
            "Leave balance retrieved successfully",
            "Failed to retrieve leave balance"
        )

        logger.info(f"Final Result Status: {result.get('status', 'unknown')}")
        logger.info("=== LEAVE BALANCE CLIENT API CALL COMPLETE ===")

        return result

    def request_leave(
        self,
        employee_id: Optional[str] = None,
        email: Optional[str] = None,
        start_date: str = None,
        end_date: str = None,
        leave_type: str = None,
        reason: str = None,
        half_day: bool = False
    ) -> Dict[str, Any]:
        """Request leave for an employee.

        Args:
            employee_id: Optional employee ID
            email: Optional employee email
            start_date: Start date of leave (YYYY-MM-DD)
            end_date: End date of leave (YYYY-MM-DD)
            leave_type: Type of leave (casual, sick, annual, etc.)
            reason: Reason for leave
            half_day: Whether this is a half-day leave

        Returns:
            Leave request information
        """
        data = {
            "startDate": start_date,
            "endDate": end_date,
            "leaveType": leave_type,
            "reason": reason,
            "halfDay": half_day
        }

        if employee_id:
            data["employeeId"] = employee_id
        if email:
            data["email"] = email

        status_code, response_data = self.post("/leave/request", data=data)
        return self.handle_response(
            status_code,
            response_data,
            "Leave request created successfully",
            "Failed to create leave request"
        )

class AttendanceClient(HRAPIClient):
    """Client for Attendance API."""

    def __init__(self):
        """Initialize the Attendance API client."""
        super().__init__(
            os.environ.get("ATTENDANCE_API_BASE_URL", DEFAULT_WAGONHR_API_URL),
            auth_type="wagonhr"
        )

    def get_attendance_report(
        self,
        employee_email: str,
        start_date: str,
        end_date: str
    ) -> Dict[str, Any]:
        """Get attendance report for an employee.

        Args:
            employee_email: Email of the employee
            start_date: Start date of the report (YYYY-MM-DD)
            end_date: End date of the report (YYYY-MM-DD)

        Returns:
            Attendance report information
        """
        params = {
            "email": employee_email,
            "startDate": start_date,
            "endDate": end_date
        }

        status_code, response_data = self.get("/attendance/report", params=params)
        return self.handle_response(
            status_code,
            response_data,
            "Attendance report retrieved successfully",
            "Failed to retrieve attendance report"
        )

    def record_attendance(
        self,
        employee_id: Optional[str] = None,
        email: Optional[str] = None,
        status: str = None,
        timestamp: Optional[str] = None,
        location: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Record attendance for an employee.

        Args:
            employee_id: Optional employee ID
            email: Optional employee email
            status: Attendance status (present, absent, late, etc.)
            timestamp: Optional timestamp (ISO format)
            location: Optional location data

        Returns:
            Attendance record information
        """
        data = {
            "status": status
        }

        if employee_id:
            data["employeeId"] = employee_id
        if email:
            data["email"] = email
        if timestamp:
            data["timestamp"] = timestamp
        if location:
            data["location"] = location

        status_code, response_data = self.post("/attendance/record", data=data)
        return self.handle_response(
            status_code,
            response_data,
            "Attendance recorded successfully",
            "Failed to record attendance"
        )

# Create singleton instances
leave_balance_client = LeaveBalanceClient()
attendance_client = AttendanceClient()
