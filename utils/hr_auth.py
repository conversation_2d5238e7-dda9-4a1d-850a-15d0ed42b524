"""
HR API Authentication Utility.

This module provides utilities for managing Microsoft tokens from frontend
and WagonHR API authentication for accessing leave balance and attendance data.
Includes Redis caching for tokens to improve performance and reduce authentication requests.
"""

import os
import json
import time
import logging
import requests
from typing import Dict, Optional, Any, Tuple

# Try to import Redis
try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    logger = logging.getLogger(__name__)
    logger.warning("Redis package not installed. Token caching will be disabled. Install with 'pip install redis'")

# Set up logger
logger = logging.getLogger(__name__)

class HRAuthManager:
    """Authentication Manager for HR APIs."""

    def __init__(
        self,
        wagonhr_api_url: str = None,
        wagonhr_username: str = None,
        wagonhr_password: str = None,
        redis_url: str = None,
        redis_password: str = None,
        use_redis: bool = None
    ):
        """Initialize the HR Authentication Manager.

        Args:
            wagonhr_api_url: WagonHR API URL
            wagonhr_username: WagonHR username
            wagonhr_password: WagonHR password
            redis_url: Redis server URL (host:port)
            redis_password: Redis server password
            use_redis: Whether to use Redis for token caching
        """
        # Microsoft token will be provided by frontend after login
        self.ms_token_from_frontend = None
        self.ms_token_expiry = 0

        # WagonHR API configuration
        self.wagonhr_api_url = wagonhr_api_url or os.environ.get("WAGONHR_API_URL", "https://api-wagonhr.mouritech.com")
        self.wagonhr_auth_url = f"{self.wagonhr_api_url}/auth/user/v3/login"
        self.wagonhr_username = wagonhr_username or os.environ.get("WAGONHR_USERNAME", "")
        self.wagonhr_password = wagonhr_password or os.environ.get("WAGONHR_PASSWORD", "")

        # Token storage
        self.wagonhr_access_token = None
        self.wagonhr_token_expiry = 0

        # Redis configuration
        self.use_redis = use_redis if use_redis is not None else os.environ.get("USE_REDIS_CACHE", "false").lower() == "true"
        self.redis_client = None

        # Redis key prefixes for tokens
        self.wagonhr_token_key = "hr_auth:wagonhr_token"

        # Initialize Redis client if available and enabled
        if self.use_redis and REDIS_AVAILABLE:
            redis_url = redis_url or os.environ.get("REDIS_URL", "localhost:6379")
            redis_password = redis_password or os.environ.get("REDIS_PASSWORD", "")

            try:
                # Parse Redis URL
                if ":" in redis_url:
                    host, port = redis_url.split(":")
                    port = int(port)
                else:
                    host = redis_url
                    port = 6379

                # Connect to Redis
                self.redis_client = redis.Redis(
                    host=host,
                    port=port,
                    password=redis_password,
                    decode_responses=True
                )

                # Test connection
                self.redis_client.ping()
                logger.info(f"Connected to Redis server at {host}:{port}")

                # Try to load tokens from Redis
                self._load_tokens_from_redis()
            except Exception as e:
                logger.error(f"Failed to connect to Redis: {str(e)}")
                self.redis_client = None
                self.use_redis = False
        elif self.use_redis and not REDIS_AVAILABLE:
            logger.warning("Redis caching is enabled but Redis package is not installed. Install with 'pip install redis'")
            self.use_redis = False

    def _load_tokens_from_redis(self) -> None:
        """Load tokens from Redis cache if available."""
        if not self.redis_client:
            return

        try:
            # Load WagonHR token
            wagonhr_token_data = self.redis_client.hgetall(self.wagonhr_token_key)
            if wagonhr_token_data and "token" in wagonhr_token_data and "expiry" in wagonhr_token_data:
                self.wagonhr_access_token = wagonhr_token_data["token"]
                self.wagonhr_token_expiry = float(wagonhr_token_data["expiry"])
                logger.info("Loaded WagonHR token from Redis cache")
        except Exception as e:
            logger.error(f"Error loading tokens from Redis: {str(e)}")

    def _save_token_to_redis(self, token_key: str, token: str, expiry: float) -> None:
        """Save token to Redis cache.

        Args:
            token_key: Redis key for the token
            token: The token to save
            expiry: Token expiry timestamp
        """
        if not self.redis_client or not self.use_redis:
            return

        try:
            # Save token data to Redis
            self.redis_client.hset(token_key, mapping={
                "token": token,
                "expiry": str(expiry)
            })

            # Set expiry for the Redis key (5 minutes after token expiry)
            ttl = int(expiry - time.time()) + 300
            if ttl > 0:
                self.redis_client.expire(token_key, ttl)

            logger.info(f"Saved token to Redis cache with key {token_key}")
        except Exception as e:
            logger.error(f"Error saving token to Redis: {str(e)}")

    def set_microsoft_token(self, token: str, expiry_timestamp: Optional[float] = None) -> None:
        """Set Microsoft token from frontend.

        Args:
            token: Microsoft access token from frontend
            expiry_timestamp: Token expiry timestamp (optional)
        """
        self.ms_token_from_frontend = token
        # If no expiry provided, assume token is valid for 1 hour
        self.ms_token_expiry = expiry_timestamp or (time.time() + 3600)
        logger.info("Microsoft token set from frontend")

    def set_wagonhr_token(self, token: str, expiry_timestamp: Optional[float] = None) -> None:
        """Set WagonHR token directly (e.g., from Microsoft token exchange).

        Args:
            token: WagonHR access token
            expiry_timestamp: Token expiry timestamp (optional)
        """
        self.wagonhr_access_token = token
        # If no expiry provided, assume token is valid for 24 hours
        self.wagonhr_token_expiry = expiry_timestamp or (time.time() + 86400)
        logger.info("WagonHR token set directly")

        # Save token to Redis if enabled
        if self.use_redis and self.redis_client:
            self._save_token_to_redis(self.wagonhr_token_key, self.wagonhr_access_token, self.wagonhr_token_expiry)

    def has_valid_microsoft_token(self) -> bool:
        """Check if we have a valid Microsoft token.

        Returns:
            True if we have a valid Microsoft token, False otherwise
        """
        current_time = time.time()
        return (self.ms_token_from_frontend is not None and
                current_time < (self.ms_token_expiry - 300))

    def authenticate_wagonhr(self) -> bool:
        """Authenticate with WagonHR API using Microsoft token exchange.

        Returns:
            True if authentication was successful, False otherwise
        """
        # Check if we already have a valid token in memory
        current_time = time.time()
        if self.wagonhr_access_token and current_time < (self.wagonhr_token_expiry - 300):
            logger.info("Using existing WagonHR token from memory")
            return True

        # Try Microsoft token exchange first if we have a valid Microsoft token
        if self.has_valid_microsoft_token():
            logger.info("Attempting WagonHR authentication using Microsoft token exchange")
            try:
                headers = {
                    "Content-Type": "application/json"
                }

                data = {
                    "accessToken": self.ms_token_from_frontend,
                    "type": "azure_ad_identifier"
                }

                response = requests.post(
                    self.wagonhr_auth_url,
                    headers=headers,
                    json=data
                )

                if response.status_code == 200:
                    response_data = response.json()
                    if response_data.get("status") == "success" and "data" in response_data:
                        user_data = response_data["data"]["user"]
                        self.wagonhr_access_token = user_data.get("token")
                        # Assuming token expires in 24 hours if not specified
                        expires_in = 86400  # 24 hours default
                        self.wagonhr_token_expiry = time.time() + expires_in
                        logger.info("WagonHR authentication successful using Microsoft token exchange")

                        # Save token to Redis if enabled
                        if self.use_redis and self.redis_client:
                            self._save_token_to_redis(self.wagonhr_token_key, self.wagonhr_access_token, self.wagonhr_token_expiry)

                        return True
                    else:
                        logger.error(f"WagonHR authentication failed: Invalid response format")
                        return False
                else:
                    logger.error(f"WagonHR authentication failed: {response.text}")
                    return False
            except Exception as e:
                logger.error(f"Error during WagonHR Microsoft token exchange: {str(e)}")
                # Fall back to username/password if available
                pass

        # Fall back to username/password authentication if Microsoft token exchange fails
        if self.wagonhr_username and self.wagonhr_password:
            logger.info("Falling back to WagonHR username/password authentication")
            try:
                headers = {
                    "Content-Type": "application/json"
                }

                data = {
                    "username": self.wagonhr_username,
                    "password": self.wagonhr_password
                }

                response = requests.post(
                    self.wagonhr_auth_url,
                    headers=headers,
                    json=data
                )

                if response.status_code == 200:
                    token_data = response.json()
                    self.wagonhr_access_token = token_data.get("token")
                    # Assuming token expires in 24 hours if not specified
                    expires_in = token_data.get("expiresIn", 86400)
                    self.wagonhr_token_expiry = time.time() + expires_in
                    logger.info("WagonHR authentication successful using username/password")

                    # Save token to Redis if enabled
                    if self.use_redis and self.redis_client:
                        self._save_token_to_redis(self.wagonhr_token_key, self.wagonhr_access_token, self.wagonhr_token_expiry)

                    return True
                else:
                    logger.error(f"WagonHR authentication failed: {response.text}")
                    return False
            except Exception as e:
                logger.error(f"Error during WagonHR username/password authentication: {str(e)}")
                return False
        else:
            logger.error("No valid authentication method available for WagonHR (no Microsoft token or username/password)")
            return False



    def get_ms_auth_headers(self) -> Dict[str, str]:
        """Get Microsoft authentication headers for API requests.

        Returns:
            Dictionary of headers including authentication
        """
        # Check if we have a valid token from frontend
        if not self.has_valid_microsoft_token():
            logger.error("No valid Microsoft token available from frontend")
            return {}

        return {
            "Authorization": f"Bearer {self.ms_token_from_frontend}",
            "Content-Type": "application/json"
        }

    def get_wagonhr_auth_headers(self) -> Dict[str, str]:
        """Get WagonHR authentication headers for API requests.

        Returns:
            Dictionary of headers including authentication
        """
        # Check if token is expired or about to expire (within 5 minutes)
        current_time = time.time()
        if current_time > (self.wagonhr_token_expiry - 300) or not self.wagonhr_access_token:
            # Try to load from Redis first if enabled
            if self.use_redis and self.redis_client:
                try:
                    wagonhr_token_data = self.redis_client.hgetall(self.wagonhr_token_key)
                    if wagonhr_token_data and "token" in wagonhr_token_data and "expiry" in wagonhr_token_data:
                        token_expiry = float(wagonhr_token_data["expiry"])
                        # Check if Redis token is still valid
                        if current_time < (token_expiry - 300):
                            self.wagonhr_access_token = wagonhr_token_data["token"]
                            self.wagonhr_token_expiry = token_expiry
                            logger.info("Using WagonHR token from Redis cache")
                            return {
                                "Authorization": f"Bearer {self.wagonhr_access_token}",
                                "Content-Type": "application/json"
                            }
                except Exception as e:
                    logger.error(f"Error loading WagonHR token from Redis: {str(e)}")

            # If not in Redis or Redis failed, authenticate
            if not self.authenticate_wagonhr():
                logger.error("Failed to refresh WagonHR authentication")
                return {}

        return {
            "Authorization": f"Bearer {self.wagonhr_access_token}",
            "Content-Type": "application/json"
        }

    def make_authenticated_request(
        self,
        method: str,
        url: str,
        auth_type: str = "wagonhr",
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None
    ) -> Tuple[int, Dict[str, Any]]:
        """Make an authenticated request to an HR API.

        Args:
            method: HTTP method (GET, POST, PUT, DELETE)
            url: URL to request
            auth_type: Authentication type to use ('microsoft' or 'wagonhr')
            data: Optional data to send in the request body
            params: Optional query parameters
            headers: Optional additional headers

        Returns:
            Tuple of (status_code, response_data)
        """
        # Get authentication headers based on auth_type
        if auth_type.lower() == "microsoft":
            auth_headers = self.get_ms_auth_headers()
        else:  # Default to WagonHR
            auth_headers = self.get_wagonhr_auth_headers()

        if not auth_headers:
            logger.error(f"Failed to get {auth_type} authentication headers")
            return 401, {"error": "Authentication failed"}

        # Combine with additional headers
        request_headers = {**auth_headers}
        if headers:
            request_headers.update(headers)

        # Log final headers (excluding sensitive token values for security)
        header_summary = {}
        for key, value in request_headers.items():
            if key.lower() == 'authorization':
                header_summary[key] = f"Bearer ***{value[-10:] if len(value) > 10 else '***'}"
            else:
                header_summary[key] = value
        logger.info(f"Final request headers: {header_summary}")

        # Generate complete curl command for debugging
        logger.info("=== COMPLETE CURL COMMAND FOR LEAVE BALANCE API ===")
        curl_cmd = f"curl -X {method.upper()} '{url}"
        if params:
            param_string = "&".join([f"{k}={v}" for k, v in params.items()])
            curl_cmd += f"?{param_string}"
        curl_cmd += "'"
        logger.info(curl_cmd)

        for key, value in request_headers.items():
            if key.lower() == 'authorization':
                masked_value = f"Bearer {value.split(' ')[-1][:20]}..." if len(value) > 20 else value
                logger.info(f"  -H '{key}: {masked_value}'")
            else:
                logger.info(f"  -H '{key}: {value}'")

        if data:
            logger.info(f"  -d '{data}'")

        logger.info("=== END CURL COMMAND ===")

        try:
            response = requests.request(
                method=method.upper(),
                url=url,
                json=data,
                params=params,
                headers=request_headers
            )

            try:
                response_data = response.json()
            except ValueError:
                response_data = {"text": response.text}

            return response.status_code, response_data
        except Exception as e:
            logger.error(f"Error making authenticated request: {str(e)}")
            return 500, {"error": str(e)}

# Create a singleton instance
hr_auth = HRAuthManager()
