"""
HR Service Desk Agent implementation.

This module contains the HR Service Desk Agent for handling HR requests
and HRMS integration.
"""

import logging
from typing import List, Optional, Dict, Any

from google.adk.agents import Agent
from google.adk.tools import transfer_to_agent

from tools.hr_tools import create_hr_case, update_hr_case, get_hr_cases, get_employee_info
from tools.leave_tools import get_leave_balance, approve_leave, reject_leave, cancel_leave, modify_leave
from tools.policy_tools import search_policies, get_policy_by_id, search_policy_by_category, ask_policy_question
from tools.attendance_tools import record_attendance, get_attendance_report, get_attendance_summary

from utils.callbacks import input_safety_guardrail, tool_safety_guardrail, combined_post_processing_callback
from utils.system_prompts import HR_SERVICE_DESK_AGENT_PROMPT

# Set up logger
logger = logging.getLogger(__name__)


def get_routine_hr_information(
    query: str,
    tool_context: Optional[object] = None
) -> Dict[str, Any]:
    """
    Handles routine HR information requests using the Flexible WagonHR Agent.

    This function serves as a unified entry point for general HR queries including
    employee information, policies, leave balances, and other HR-related questions.

    Args:
        query: The HR-related query from the user
        tool_context: Tool context for accessing session state

    Returns:
        Dictionary containing operation status and response
    """
    try:
        logger.info("=== ROUTINE HR INFORMATION REQUEST ===")
        logger.info(f"Processing HR query: {query}")

        # Import the flexible agent
        from tools.flexible_wagonhr_agent import process_wagonhr_request

        # Use the flexible agent to process the HR query
        result = process_wagonhr_request(query, tool_context)

        if result.get("status") == "success":
            logger.info("HR query processed successfully via Flexible WagonHR Agent")
            return result
        else:
            logger.error(f"Failed to process HR query: {result.get('message', 'Unknown error')}")
            return result

    except Exception as e:
        logger.error(f"Error processing HR query: {e}")
        return {
            "status": "error",
            "message": f"Error processing HR query: {str(e)}"
        }


def create_hr_service_desk_agent(
    model_name: str = "gemini-2.0-flash",
    safety_callbacks: bool = True
) -> Agent:
    """Creates an HR Service Desk Agent.

    Args:
        model_name: Name of the LLM model to use.
        safety_callbacks: Whether to enable safety callbacks.

    Returns:
        A configured HR Service Desk Agent.
    """
    logger.info(f"Creating HR Service Desk Agent with model {model_name}")

    # Define tools
    tools = [
        # HR information tools
        get_routine_hr_information,

        # Transfer tool
        transfer_to_agent
    ]

    # Define callbacks
    callbacks = {}
    if safety_callbacks:
        callbacks["before_model_callback"] = input_safety_guardrail
        callbacks["before_tool_callback"] = tool_safety_guardrail

    # Always include the combined post-processing callback
    callbacks["after_model_callback"] = combined_post_processing_callback

    # Create the agent
    agent = Agent(
        name="hr_service_desk_agent",
        model=model_name,
        description="Agent for managing HR requests and HRMS integration.",
        instruction=HR_SERVICE_DESK_AGENT_PROMPT,
        output_key="hr_service_desk_agent_response",
        tools=tools,
        **callbacks
    )

    logger.info("HR Service Desk Agent created successfully")
    return agent
