"""
Root Agent implementation.

This module contains the Root/Orchestrator Agent that serves as the main
entry point and coordinator for the HR solution.
"""

import logging
from typing import List, Optional

from google.adk.agents import Agent

from agents.hr_service_desk_agent import create_hr_service_desk_agent
from agents.leave_management_agent import create_leave_management_agent
from agents.attendance_management_agent import create_attendance_management_agent
from utils.callbacks import input_safety_guardrail, combined_post_processing_callback
from utils.audit_setup import setup_agent_audit, setup_agents_audit
from utils.system_prompts import ROOT_AGENT_PROMPT

# Set up logger
logger = logging.getLogger(__name__)


def create_root_agent(
    model_name: str = "gemini-2.0-flash",
    safety_callbacks: bool = True,
    sub_agent_model: str = "gemini-2.0-flash"
) -> Agent:
    """Creates the Root/Orchestrator Agent.

    Args:
        model_name: Name of the LLM model to use for the root agent.
        safety_callbacks: Whether to enable safety callbacks.
        sub_agent_model: Name of the LLM model to use for sub-agents.

    Returns:
        A configured Root Agent with all sub-agents.
    """

    # Create sub-agents
    hr_service_desk_agent = create_hr_service_desk_agent(
        model_name=sub_agent_model,
        safety_callbacks=safety_callbacks
    )

    leave_management_agent = create_leave_management_agent(
        model_name=sub_agent_model,
        safety_callbacks=safety_callbacks
    )

    attendance_management_agent = create_attendance_management_agent(
        model_name=sub_agent_model,
        safety_callbacks=safety_callbacks
    )

    # Define callbacks
    callbacks = {}
    if safety_callbacks:
        callbacks["before_model_callback"] = input_safety_guardrail

    # Use the combined callback for post-processing
    callbacks["after_model_callback"] = combined_post_processing_callback

    # Define global instruction for all agents
    global_instruction = """
    CRITICAL WORKFLOW PATTERN FOR ALL AGENTS:

    1. HR inquiries are handled directly without creating cases or tickets
    2. For leave management requests, transfer directly to the Leave Management Agent
    3. For attendance management requests, transfer directly to the Attendance Management Agent
    4. For general HR inquiries, handle with the HR Service Desk Agent
    5. After specialized agents complete their tasks, they can provide the response directly to the user

    This pattern ensures efficient handling of HR inquiries without unnecessary case creation.
    """

    # Create the root agent
    root_agent = Agent(
        name="hr_root_agent",
        model=model_name,
        description="Main orchestrator agent for the HR solution that delegates to specialized agents.",
        instruction=ROOT_AGENT_PROMPT,
        global_instruction=global_instruction,
        output_key="root_agent_response",
        sub_agents=[hr_service_desk_agent, leave_management_agent, attendance_management_agent],
        **callbacks
    )

    # Set up audit trail for all agents
    logger.info("Setting up audit trail for all agents")
    setup_agent_audit(root_agent)
    setup_agents_audit([hr_service_desk_agent, leave_management_agent, attendance_management_agent])
    logger.info("Audit trail setup complete")

    return root_agent