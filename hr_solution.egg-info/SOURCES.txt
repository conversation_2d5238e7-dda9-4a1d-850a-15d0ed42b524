README.md
pyproject.toml
agents/__init__.py
agents/attendance_management_agent.py
agents/hr_service_desk_agent.py
agents/leave_management_agent.py
agents/root_agent.py
api/audit_routes.py
examples/audit_trail_example.py
examples/header_flow_example.py
examples/hr_agent_integration.py
google/__init__.py
google/adk/__init__.py
google/adk/tools/__init__.py
google/adk/tools/tool_context.py
hr_solution.egg-info/PKG-INFO
hr_solution.egg-info/SOURCES.txt
hr_solution.egg-info/dependency_links.txt
hr_solution.egg-info/requires.txt
hr_solution.egg-info/top_level.txt
models/__init__.py
models/audit_models.py
models/hr_policy.py
models/otp_models.py
runners/__init__.py
runners/runner_service.py
scripts/check_audit_db.py
scripts/check_db_config.py
scripts/check_postgres_db.py
scripts/check_postgres_user.py
scripts/check_session_messages.py
scripts/cleanup_duplicate_prompts.py
scripts/create_policy_vector_tables.py
scripts/deploy_to_aws.py
scripts/init_audit_db.py
scripts/migrate_otp_table.py
scripts/recreate_db_schema.py
scripts/simple_audit_test.py
scripts/test_all_audit_tables.py
scripts/test_audit_db.py
scripts/test_audit_integration.py
scripts/test_password_reset_audit.py
scripts/vectorize_policies.py
services/__init__.py
services/audit_service.py
services/policy_vector_service.py
services/session_service.py
services/vertex_ai_session_service.py
tests/test_authorization_check.py
tests/test_aws_opensearch_tool.py
tests/test_dynamic_wagonhr_agent.py
tests/test_hr_agent.py
tests/test_hr_auth.py
tests/test_password_reset_permissions.py
tests/test_user_roles.py
tools/__init__.py
tools/attendance_tools.py
tools/aws_opensearch_policy_tool.py
tools/flexible_wagonhr_agent.py
tools/geo_tools.py
tools/hr_tools.py
tools/leave_tools.py
tools/policy_tools.py
tools/user_roles.py
utils/__init__.py
utils/audit_callbacks.py
utils/audit_setup.py
utils/aws_utils.py
utils/callbacks.py
utils/date_parser.py
utils/db_config.py
utils/env_loader.py
utils/env_setup.py
utils/hr_api_client.py
utils/hr_auth.py
utils/logging_config.py
utils/session_helpers.py
utils/system_prompts.py
venv/bin/jp.py
venv_python39_backup/bin/jp.py