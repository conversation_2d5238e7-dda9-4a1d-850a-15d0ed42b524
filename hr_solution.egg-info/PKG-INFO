Metadata-Version: 2.4
Name: hr-solution
Version: 2.1.82
Summary: HR AI Assistant with Google ADK integration
Author-email: Mouritech <<EMAIL>>
Project-URL: Homepage, https://github.com/mouritech/hr-solution
Project-URL: Documentation, https://hr-solution.mouritech.com/docs
Project-URL: Repository, https://github.com/mouritech/hr-solution.git
Project-URL: Bug Tracker, https://github.com/mouritech/hr-solution/issues
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Requires-Python: >=3.8
Description-Content-Type: text/markdown
Requires-Dist: fastapi>=0.68.0
Requires-Dist: uvicorn[standard]>=0.15.0
Requires-Dist: pydantic>=1.8.0
Requires-Dist: sqlalchemy>=1.4.0
Requires-Dist: google-adk[vertexai]>=0.1.0
Requires-Dist: requests>=2.25.0
Requires-Dist: python-dotenv>=0.19.0
Provides-Extra: dev
Requires-Dist: pytest>=6.0; extra == "dev"
Requires-Dist: pytest-asyncio>=0.15.0; extra == "dev"
Requires-Dist: black>=21.0.0; extra == "dev"
Requires-Dist: flake8>=3.9.0; extra == "dev"

# HR AI Assistant Solution

An AI-powered HR Assistant system built with Google's Agent Development Kit (ADK), integrated with HRMS for leave management, attendance tracking, and employee data management.

## Overview

This project implements a multi-agent AI system that automates common HR tasks, including:

- Leave management and approvals
- Attendance tracking and verification
- HR policy queries and assistance
- Workload-based leave suggestions
- Multi-timezone attendance handling
- Location-based attendance verification

The system uses a hierarchical agent architecture with specialized agents for different tasks, all orchestrated by a root agent.

## Architecture

The solution is built using a multi-agent architecture:

1. **Root Agent**: Orchestrates the workflow and delegates to specialized agents
2. **HR Service Desk Agent**: Handles general HR inquiries and provides employee information
3. **Leave Management Agent**: Processes leave requests, approvals, and suggestions
4. **Attendance Management Agent**: Handles attendance tracking, verification, and reporting

### Workflow

The system follows a streamlined workflow:

1. HR inquiries are handled directly without creating cases or tickets
2. For leave management requests, the Root Agent transfers directly to the Leave Management Agent
3. For attendance management requests, the Root Agent transfers directly to the Attendance Management Agent
4. For general HR inquiries, the HR Service Desk Agent handles them directly
5. Specialized agents provide responses directly to the user without creating cases

## Features

- **Leave Management Automation (P1)**: Automates leave requests and approvals, recommends leave types, shows real-time balance, routes to manager, ensures compliance
- **Virtual HR Assistant (P1)**: AI-powered chatbot support for leave and attendance queries, providing 24/7 support via chat or voice without creating cases or tickets
- **AI Dashboards & Reports (P2)**: Dashboards that analyze leave and attendance data, flag anomalies like frequent lateness, leave near holidays, irregular logins
- **Auto-Suggest Leave Based on Workload (P1)**: Suggests best leave dates aligned with project workload and sprint cycles
- **Multi-Timezone Attendance Handling (P2)**: Adjusts attendance logs based on employee time zone
- **Geo-Fencing for Attendance Verification (P2)**: Detects remote/hybrid attendance anomalies based on location
- **Conversation Context**: Maintains context throughout the conversation
- **Security**: Implements safety guardrails for sensitive operations

## Prerequisites

- Python 3.8+
- Google Cloud account with Vertex AI access (or Google API key)
- Service account credentials (if using Vertex AI)
- HRMS API access for leave and attendance data
- Project management system API access (for workload data)

## Installation

1. Clone the repository:
   ```
   git clone https://gitlab.mouritech.com/hr-agent/hr-agent.git
   cd hr-agent/hr-agent
   ```

2. Create a virtual environment:
   ```
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```
   pip install -r hr-agent/requirements.txt
   ```

4. Set up environment variables:
   - Copy the `.env.example` file to `.env`
   - Configure either Vertex AI credentials or Google API key
   - Configure HRMS API credentials

## Configuration

The application can be configured using environment variables in the `.env` file or using Google Cloud Secret Manager for different environments (dev, staging, prod).

### Option 1: Local Environment Variables (`.env` file)

Create a `.env` file in the root directory with the following variables:

```
# Environment Configuration
ENVIRONMENT=dev  # dev, staging, prod

# Google API Key - Required if not using Vertex AI
GOOGLE_API_KEY=your-google-api-key-here

# Google Vertex AI Configuration (Optional)
GOOGLE_GENAI_USE_VERTEXAI=False
GOOGLE_CLOUD_PROJECT=your-project-id
GOOGLE_CLOUD_LOCATION=us-central1
# GOOGLE_APPLICATION_CREDENTIALS=path/to/your/service-account-key.json

# Secret Manager Configuration (Optional)
USE_SECRET_MANAGER=False

# Model Configuration
ROOT_MODEL=gemini-2.0-flash
SUB_AGENT_MODEL=gemini-2.0-flash

# HRMS API Configuration
HRMS_API_URL=https://your-hrms-api-url.com
HRMS_API_KEY=your-hrms-api-key
HRMS_API_SECRET=your-hrms-api-secret

# Project Management API Configuration
PM_API_URL=https://your-project-management-api-url.com
PM_API_KEY=your-project-management-api-key

# Logging Configuration
LOG_LEVEL=INFO  # DEBUG, INFO, WARNING, ERROR, CRITICAL
```

### Option 2: Google Cloud Secret Manager (recommended for production)

For production environments, it's recommended to store secrets in Google Cloud Secret Manager. This allows you to manage different configurations for different environments (dev, staging, prod).

1. Install the Secret Manager library:
   ```
   pip install google-cloud-secret-manager
   ```

2. Set up your secrets in Secret Manager using the provided script:
   ```
   python scripts/setup_secrets.py --project-id your-project-id --environment dev
   ```

3. Create a `.env` file with minimal configuration:
   ```
   ENVIRONMENT=dev  # or staging, prod
   USE_SECRET_MANAGER=True
   GOOGLE_CLOUD_PROJECT=your-project-id
   GOOGLE_APPLICATION_CREDENTIALS=path/to/your/service-account-key.json
   ```

The application will automatically load the appropriate secrets from Secret Manager based on the specified environment.

### Authentication Options

#### Using Vertex AI (recommended for production)

```
GOOGLE_GENAI_USE_VERTEXAI=True
GOOGLE_CLOUD_PROJECT=your-project-id
GOOGLE_CLOUD_LOCATION=us-central1
GOOGLE_APPLICATION_CREDENTIALS=path/to/your/service-account-key.json
```

#### Using API Key (simpler for development)

```
GOOGLE_GENAI_USE_VERTEXAI=False
GOOGLE_API_KEY=your-google-api-key-here
```

### Additional Configuration

```
LOG_LEVEL=INFO  # DEBUG, INFO, WARNING, ERROR, CRITICAL
ROOT_MODEL=gemini-2.0-flash  # Model for root agent
SUB_AGENT_MODEL=gemini-2.0-flash  # Model for sub-agents
```

## Usage

### Running the Interactive Demo

Run the application from the root directory:

```
python -m main
```

This starts an interactive console where you can type requests and receive responses from the HR AI Assistant.

Example interactions:

1. **Leave Request**:
   ```
   You: I want to apply for leave from June 15 to June 20.
   ```

2. **Attendance Query**:
   ```
   You: What's my attendance status for this month?
   ```

3. **Leave Suggestion**:
   ```
   You: When would be a good time to take leave considering my current project schedule?
   ```

4. **HR Policy Question**:
   ```
   You: How many casual leaves am I eligible for this year?
   ```

Type `exit` to quit the application.

### Test Accounts

For testing the HR functionality, you can use these mock accounts:

- `<EMAIL>` - A regular employee with standard leave balance
- `<EMAIL>` - A manager who can approve leave requests
- `<EMAIL>` - An employee with special leave considerations
- `<EMAIL>` - An invalid account (not in the system)

### Mock Projects

For testing the workload-based leave suggestion functionality:

- `Project Alpha` - Sprint cycle from 1st to 15th of each month
- `Project Beta` - Ongoing maintenance project with no fixed sprints
- `Project Gamma` - Critical project with upcoming deadline on July 30



## Project Structure

```
/
├── __init__.py           # Package initialization
├── main.py               # Main application entry point
├── requirements.txt      # Project dependencies
├── agents/               # Agent implementations
│   ├── __init__.py
│   ├── root_agent.py     # Root/Orchestrator agent
│   ├── hr_service_desk_agent.py
│   ├── leave_management_agent.py
│   └── attendance_management_agent.py
├── tools/                # Tool implementations
│   ├── __init__.py
│   ├── hrms.py           # HRMS integration tools
│   ├── leave_tools.py    # Leave management tools
│   ├── attendance_tools.py # Attendance tracking tools
│   ├── project_tools.py  # Project management integration
│   ├── geo_tools.py      # Geo-fencing and location tools
│   └── hr_api.py         # HR API integration
├── runners/              # Runner implementations
│   ├── __init__.py
│   └── runner_service.py
├── services/             # Service implementations
│   ├── __init__.py
│   ├── session_service.py
│   └── reporting_service.py # Reporting and dashboard service
├── scripts/              # Utility scripts
│   └── setup_secrets.py  # Script to set up secrets in Secret Manager
└── utils/                # Utility modules
    ├── __init__.py
    ├── callbacks.py      # Safety and processing callbacks
    ├── env_loader.py     # Environment variable loader
    ├── env_setup.py      # Environment setup utilities
    ├── hr_api_client.py  # HR API client
    ├── hr_auth.py        # HR authentication utilities
    ├── logging_config.py # Logging configuration
    ├── secret_manager.py # Secret Manager utilities
    └── system_prompts.py # Agent system prompts
```

## Development

### Adding New Agents

To add a new specialized agent:

1. Create a new agent module in the `agents/` directory
2. Implement the agent creation function
3. Add the agent to the root agent in `root_agent.py`
4. Update the system prompts in `utils/system_prompts.py`

### Adding New Tools

To add new tools:

1. Create or update a module in the `tools/` directory
2. Implement the tool function with proper type annotations
3. Add the tool to the appropriate agent

### Adding New HR Features

To add new HR features:

1. Identify the appropriate agent for the feature (leave, attendance, or general HR)
2. Implement the necessary tools in the `tools/` directory
3. Update the agent's system prompt to include the new capability
4. Add any required API integrations to the configuration

## Testing

Run tests using pytest:

```
pytest
```

## Limitations

- This implementation uses mock backends for HRMS and project management systems
- In a production environment, these would be replaced with actual API integrations
- The geo-fencing and multi-timezone functionality is simulated for demonstration purposes
- The workload-based leave suggestion uses simplified project data



## Implementation Status

The current implementation includes:

1. **Fully Implemented**:
   - Leave Management Automation (P1)
   - Auto-Suggest Leave Based on Workload (P1)
   - Virtual HR Assistant (P1) - Basic functionality

2. **Partially Implemented**:
   - Multi-Timezone Attendance Handling (P2)
   - Geo-Fencing for Attendance Verification (P2)
   - AI Dashboards & Reports (P2)

The implementation uses mock data instead of integrating with existing tools, as requested. The mock data structures are well-defined for employee information, leave management, attendance tracking, and project workload.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Technology Stack

- **Google Agent Development Kit (ADK)**: Framework for building multi-agent AI systems
- **Gemini 2.0 Flash**: Large language model powering the agents
- **Python**: Core programming language
- **FastAPI**: Web framework for API development (for future web interface)
- **Google Vertex AI**: Cloud platform for AI model hosting and inference
- **HRMS API**: Integration with HR management systems
- **Geolocation Services**: For attendance verification
- **Project Management APIs**: For workload-based leave suggestions
- **Microsoft Azure AD OAuth**: Authentication for Microsoft-based HR services
- **WagonHR API Authentication**: Authentication for leave balance and attendance data

## Third-Party Integrations

The HR Agent integrates with the following third-party systems:

1. **HRMS (Human Resource Management System)**: For employee data, leave management, and attendance tracking
2. **Project Management System**: For workload information and project schedules
3. **Email Service**: For sending notifications and updates


### API Clients

The HR Agent uses a dedicated HR API Client for interacting with HR-specific APIs (leave balance, attendance, etc.). The client handles authentication, request formatting, and response parsing.

### Authentication for HR APIs

The HR Agent uses two authentication mechanisms for accessing HR APIs:

1. **Microsoft Token from Frontend**: Microsoft access tokens are provided by the frontend after user login
2. **WagonHR API Authentication**: For authenticating with the WagonHR API for leave balance and attendance data

#### Configuration

To configure the authentication:

1. Copy the `.env.example` file to `.env`
2. Update the following settings in the `.env` file:

   **Microsoft Token**: No configuration required - tokens are provided by the frontend after user login.

   **WagonHR API Configuration**:
   - `WAGONHR_API_URL`: The WagonHR API URL (default: "https://api-wagonhr.mouritech.com")
   - `WAGONHR_USERNAME`: Your WagonHR username
   - `WAGONHR_PASSWORD`: Your WagonHR password

   **HR API URLs**:
   - `LEAVE_API_BASE_URL`: The leave API URL (default: "https://api-wagonhr.mouritech.com")
   - `ATTENDANCE_API_BASE_URL`: The attendance API URL (default: "https://api-wagonhr.mouritech.com")

#### Redis Token Caching

The HR Agent uses Redis to cache authentication tokens, which improves performance and reduces the number of authentication requests to the external APIs. To configure Redis caching:

1. Ensure Redis is installed and running
2. Update the following settings in the `.env` file:
   - `USE_REDIS_CACHE`: Set to `true` to enable Redis caching
   - `REDIS_URL`: Redis server URL in the format `host:port` (default: `localhost:6379`)
   - `REDIS_PASSWORD`: Redis server password (if required)

If Redis is not available or the Redis package is not installed, the system will automatically fall back to in-memory token storage.

## Acknowledgements

- Google Agent Development Kit (ADK) team
- Gemini AI models
- MouriTech team for project support
