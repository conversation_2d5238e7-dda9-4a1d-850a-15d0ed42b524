# Flexible WagonHR API Agent Documentation

## Overview

The Flexible WagonHR API Agent is a revolutionary approach that eliminates the need for hardcoded API tools or complex Swagger parsing. Instead, you simply provide the API endpoint details, and the agent dynamically constructs requests and calls the APIs based on natural language user input.

## 🚀 **Key Benefits**

### **✅ No Hardcoding Required**
- No need to create individual functions for each API endpoint
- No manual request object construction
- No hardcoded parameter mappings

### **✅ Dynamic API Discovery**
- Add new endpoints on-the-fly
- Modify existing endpoints without code changes
- Support for any HTTP method (GET, POST, PUT, DELETE, PATCH)

### **✅ Intelligent Parameter Extraction**
- Automatically extracts parameters from natural language
- Supports multiple date formats
- Handles complex parameter combinations

### **✅ Real-Time Flexibility**
- Call any WagonHR API with just endpoint details
- No deployment required for new APIs
- Instant integration of new features

## 🔧 **Core Components**

### **1. FlexibleWagonHRAgent Class**
Main orchestrator that handles:
- Endpoint management
- Parameter extraction
- API call execution
- Response processing

### **2. Three Usage Patterns**

#### **Pattern 1: Predefined Endpoints**
```python
from tools.flexible_wagonhr_agent import process_wagonhr_request

# Agent automatically finds the right endpoint and extracts parameters
result = process_wagonhr_request("How many leave days do I have?")
```

#### **Pattern 2: Custom API Calls**
```python
from tools.flexible_wagonhr_agent import call_custom_api

# Provide endpoint details and let agent handle the rest
endpoint_details = {
    "path": "/api/hrms/custom/endpoint",
    "method": "POST",
    "description": "Custom operation",
    "parameters": ["param1", "param2"]
}

result = call_custom_api(endpoint_details, "User request with param1 and param2 values")
```

#### **Pattern 3: Add Permanent Endpoints**
```python
from tools.flexible_wagonhr_agent import add_custom_endpoint, process_wagonhr_request

# Add endpoint once
endpoint_config = {
    "path": "/api/hrms/new/feature",
    "method": "GET", 
    "description": "New feature endpoint",
    "parameters": ["feature_param"]
}

add_custom_endpoint("new_feature", endpoint_config)

# Now use it like any predefined endpoint
result = process_wagonhr_request("Use new feature with some parameter")
```

## 📊 **Predefined Endpoints**

The agent comes with these predefined WagonHR endpoints:

| Endpoint Key | Method | Path | Description |
|-------------|--------|------|-------------|
| `leave_balance` | GET | `/api/hrms/leave/balance` | Get employee leave balance |
| `leave_request` | POST | `/api/hrms/leave/request` | Submit a leave request |
| `leave_history` | GET | `/api/hrms/leave/history` | Get employee leave history |
| `attendance_record` | POST | `/api/hrms/attendance/record` | Record employee attendance |
| `attendance_report` | GET | `/api/hrms/attendance/report` | Get employee attendance report |
| `employee_profile` | GET | `/api/hrms/employee/profile` | Get employee profile information |

## 🧠 **Intelligent Parameter Extraction**

### **Date Recognition**
```python
# Multiple date formats supported
"from 2024-01-15 to 2024-01-17"     # YYYY-MM-DD
"from 01/15/2024 to 01/17/2024"     # MM/DD/YYYY  
"from 15-01-2024 to 17-01-2024"     # DD-MM-YYYY
```

### **Leave Type Detection**
```python
# Automatically recognizes leave types
"casual leave"     → leaveType: "casual"
"sick leave"       → leaveType: "sick"
"annual leave"     → leaveType: "annual"
"vacation"         → leaveType: "vacation"
```

### **Status Recognition**
```python
# Attendance status detection
"present"          → status: "present"
"absent"           → status: "absent"
"work from home"   → status: "remote"
"late"             → status: "late"
```

### **Reason Extraction**
```python
# Natural language reason extraction
"because I'm sick"           → reason: "I'm sick"
"for medical appointment"    → reason: "medical appointment"
"due to family emergency"    → reason: "family emergency"
```

### **Complex Parameter Extraction**
```python
# Single request, multiple parameters
"Apply for casual half-day leave from 2024-03-15 to 2024-03-16 because of medical appointment at downtown clinic"

# Extracts:
# startDate: "2024-03-15"
# endDate: "2024-03-16"
# leaveType: "casual"
# reason: "medical appointment"
# location: "downtown clinic"
# halfDay: true
```

## 🔗 **API Integration**

### **Authentication**
```python
# Automatic WagonHR authentication
headers = {
    "Authorization": f"Bearer {hr_auth.wagonhr_access_token}",
    "Content-Type": "application/json",
    "Accept": "application/json",
    "x-tenantid": "mouritech",
    "x-entityid": "US-MT-002"
}
```

### **Request Construction**
```python
# GET requests - parameters as query params
requests.get(url, headers=headers, params=parameters)

# POST/PUT requests - parameters as JSON body
requests.post(url, headers=headers, json=parameters)
```

### **Response Handling**
```python
# Standardized response format
{
    "status": "success|error",
    "data": {...},  # API response data
    "endpoint": "endpoint_key",
    "message": "Success message"
}
```

## 💡 **Usage Examples**

### **Example 1: Leave Management**
```python
# Check leave balance
result = process_wagonhr_request("How many leave days do I have?")

# Apply for leave
result = process_wagonhr_request(
    "I want to apply for casual leave from 2024-02-15 to 2024-02-17 because I'm sick"
)

# Check leave history
result = process_wagonhr_request("Show me my leave history for last month")
```

### **Example 2: Attendance Management**
```python
# Record attendance
result = process_wagonhr_request("Record my attendance as present today")

# Generate report
result = process_wagonhr_request("Generate my attendance report for January 2024")
```

### **Example 3: Custom API Integration**
```python
# New timesheet API
timesheet_endpoint = {
    "path": "/api/hrms/timesheet/submit",
    "method": "POST",
    "description": "Submit employee timesheet",
    "parameters": ["date", "hoursWorked", "projectCode", "description"]
}

result = call_custom_api(
    timesheet_endpoint,
    "Submit timesheet for 2024-02-15 with 8 hours worked on project ABC123 for development work"
)
```

### **Example 4: Employee Search**
```python
# Custom employee search
search_endpoint = {
    "path": "/api/hrms/employee/search",
    "method": "GET",
    "description": "Search for employees",
    "parameters": ["query", "department", "limit"]
}

result = call_custom_api(
    search_endpoint,
    "Search for employees in IT department with query 'John' and limit 10"
)
```

## 🔧 **Advanced Configuration**

### **Custom Headers**
```python
endpoint_details = {
    "path": "/api/hrms/custom/endpoint",
    "method": "POST",
    "description": "Custom operation",
    "parameters": ["param1"],
    "headers": {
        "Custom-Header": "custom-value"
    }
}
```

### **Custom Base URL**
```python
endpoint_details = {
    "path": "/api/custom/endpoint",
    "method": "GET",
    "description": "External API",
    "base_url": "https://external-api.example.com"
}
```

### **Parameter Validation**
```python
endpoint_details = {
    "path": "/api/hrms/endpoint",
    "method": "POST",
    "description": "Validated endpoint",
    "parameters": ["required_param1", "optional_param2"],
    "required_parameters": ["required_param1"]  # Future enhancement
}
```

## 📈 **Performance & Scalability**

### **Advantages Over Traditional Approaches**

| **Traditional Approach** | **Flexible Agent Approach** |
|-------------------------|----------------------------|
| Create individual functions | Single agent handles all APIs |
| Hardcode request objects | Dynamic request construction |
| Manual parameter mapping | Intelligent parameter extraction |
| Code deployment for new APIs | Instant API integration |
| Fixed function signatures | Natural language input |
| Manual error handling | Standardized error management |

### **Scalability Benefits**
- **Unlimited APIs**: Support any number of endpoints
- **Zero Deployment**: Add new APIs without code changes
- **Instant Updates**: Modify endpoints in real-time
- **Language Agnostic**: Works with any API specification

## 🛡️ **Error Handling**

### **Common Error Scenarios**
```python
# Authentication failure
{
    "status": "error",
    "message": "Failed to authenticate with WagonHR"
}

# Unknown endpoint
{
    "status": "error", 
    "message": "Could not determine the appropriate API endpoint",
    "available_endpoints": ["leave_balance", "leave_request", ...]
}

# API call failure
{
    "status": "error",
    "message": "API call failed with status 400",
    "details": "Invalid request parameters"
}
```

## 🔄 **Session Management**
```python
# Automatic session storage
tool_context.state["last_wagonhr_request"] = {
    "user_request": "Check my leave balance",
    "endpoint": "leave_balance",
    "parameters": {},
    "result": {...},
    "timestamp": "2024-01-15T10:30:00"
}
```

## 🚀 **Future Enhancements**

### **Planned Features**
1. **Parameter Validation**: Automatic validation of required parameters
2. **Response Caching**: Cache responses for improved performance
3. **Batch Operations**: Support for multiple API calls in one request
4. **Webhook Support**: Real-time notifications and callbacks
5. **API Versioning**: Support for multiple API versions

### **Advanced NLP**
1. **Context Awareness**: Remember previous conversations
2. **Intent Clarification**: Ask for clarification when ambiguous
3. **Multi-Language**: Support for multiple natural languages
4. **Voice Integration**: Voice-to-API conversion

## 📝 **Best Practices**

### **Endpoint Definition**
```python
# Good endpoint definition
{
    "path": "/api/hrms/clear/endpoint",
    "method": "POST",
    "description": "Clear, descriptive operation name",
    "parameters": ["well_named_param1", "descriptive_param2"]
}
```

### **Parameter Naming**
- Use clear, descriptive parameter names
- Follow camelCase convention
- Include expected data types in documentation

### **Error Handling**
- Always check response status
- Provide meaningful error messages
- Include suggestions for resolution

## 🎯 **Conclusion**

The Flexible WagonHR API Agent represents a paradigm shift in API integration:

- **No More Hardcoding**: Dynamic request construction eliminates manual coding
- **Instant Integration**: New APIs available immediately with just endpoint details
- **Natural Language**: Users interact in plain English, not technical syntax
- **Unlimited Scalability**: Support any number of APIs without code changes
- **Future-Proof**: Adapts to API changes and new requirements automatically

This approach transforms WagonHR API integration from a complex development task into a simple configuration exercise, enabling rapid deployment and seamless user experiences.
