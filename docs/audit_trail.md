# Audit Trail System

The Audit Trail System provides comprehensive tracking and logging of all agent actions within the ITSM solution. This document describes the system architecture, database schema, and usage instructions.

## Overview

The Audit Trail System records detailed information about:

- User sessions
- User messages
- Agent responses
- Tool calls and their results
- Entity operations (e.g., password resets, ticket creation)

This information can be used for:

- Security auditing
- Compliance reporting
- Debugging and troubleshooting
- Performance monitoring
- User behavior analysis

## Database Schema

The system uses the following database tables:

### agent_registry

Stores information about registered agents.

| Column | Type | Description |
|--------|------|-------------|
| id | Integer | Primary key |
| name | String | Agent name |
| description | Text | Agent description |
| is_active | Boolean | Whether the agent is active |
| config | JSON | Agent configuration |
| created_at | DateTime | Creation timestamp |
| modified_at | DateTime | Last modification timestamp |

### agent_prompt

Stores prompts used by agents.

| Column | Type | Description |
|--------|------|-------------|
| id | Integer | Primary key |
| agent_id | Integer | Foreign key to agent_registry |
| prompt_type | String | Type of prompt (e.g., 'system', 'user') |
| prompt_text | Text | Text of the prompt |
| created_at | DateTime | Creation timestamp |

### agent_tool

Stores tools available to agents.

| Column | Type | Description |
|--------|------|-------------|
| id | Integer | Primary key |
| agent_id | Integer | Foreign key to agent_registry |
| tool_name | String | Name of the tool |
| tool_description | Text | Description of the tool |
| tool_config | JSON | Tool configuration |
| created_at | DateTime | Creation timestamp |

### session

Stores information about user sessions.

| Column | Type | Description |
|--------|------|-------------|
| id | String | Primary key (UUID) |
| user_identifier | String | User identifier (e.g., email) |
| agent_id | Integer | Foreign key to agent_registry |
| start_time | DateTime | Session start timestamp |
| end_time | DateTime | Session end timestamp |

### session_message

Stores messages exchanged during a session.

| Column | Type | Description |
|--------|------|-------------|
| id | Integer | Primary key |
| session_id | String | Foreign key to session |
| actor_type | String | Type of actor (e.g., 'user', 'agent') |
| message | Text | Message text |
| timestamp | DateTime | Message timestamp |

### audit_event

Stores audit events for agent actions.

| Column | Type | Description |
|--------|------|-------------|
| id | Integer | Primary key |
| session_id | String | Foreign key to session |
| agent_id | Integer | Foreign key to agent_registry |
| user_identifier | String | User identifier |
| actor_type | String | Type of actor (e.g., 'agent', 'tool') |
| actor_id | String | ID of the actor |
| action_type | String | Type of action |
| entity_type | String | Type of entity affected |
| entity_id | String | ID of entity affected |
| status | String | Status of the event |
| error_message | Text | Error message (if any) |
| context_data | JSON | Additional context data |
| created_at | DateTime | Creation timestamp |
| modified_at | DateTime | Last modification timestamp |

## API Endpoints

The system provides the following API endpoints:

### Audit Events

- `GET /audit/events` - Get audit events based on filters
  - Query parameters: `session_id`, `agent_id`, `user_identifier`, `actor_type`, `action_type`, `status`, `start_time`, `end_time`, `limit`, `offset`

### Sessions

- `GET /audit/sessions/{session_id}` - Get the complete history of a session
  - Query parameters: `include_messages`, `include_events`
- `GET /audit/sessions` - Get session IDs based on filters
  - Query parameters: `user_identifier`, `agent_id`, `start_time`, `end_time`, `limit`, `offset`

### Agents

- `GET /audit/agents` - Get registered agents
  - Query parameters: `is_active`, `limit`, `offset`

### Reports

- `GET /audit/reports/user-activity/{user_identifier}` - Get a report of user activity
  - Query parameters: `start_time`, `end_time`
- `GET /audit/reports/tool-usage` - Get a report of tool usage
  - Query parameters: `agent_id`, `start_time`, `end_time`

## Usage

### Recording Audit Events

The system provides callback functions that can be registered with the agent framework:

- `audit_input_callback` - Records user inputs
- `audit_tool_callback` - Records tool calls
- `audit_response_callback` - Records agent responses
- `record_tool_result` - Records tool results

Example usage:

```python
from utils.audit_callbacks import (
    audit_input_callback,
    audit_tool_callback,
    audit_response_callback,
    record_tool_result
)

# Register callbacks with agent
agent.add_request_callback(audit_input_callback)
agent.add_tool_callback(audit_tool_callback)
agent.add_response_callback(audit_response_callback)

# Record tool result
record_tool_result(
    tool_name="reset_password",
    agent_name="ITSM Agent",
    session_id=session_id,
    user_id=user_id,
    args={"email": "<EMAIL>"},
    result={"success": True, "message": "Password reset successfully"},
    status="success",
    entity_type="password",
    entity_id="john.doe"
)
```

### Querying Audit Events

Example usage:

```python
from services.audit_service import AuditService

# Get audit events for a user
events = AuditService.get_audit_events(
    user_identifier="<EMAIL>",
    start_time=datetime.datetime.utcnow() - datetime.timedelta(days=7),
    limit=100
)

# Get session history
history = AuditService.get_session_history(
    session_id="123e4567-e89b-12d3-a456-426614174000",
    include_messages=True,
    include_events=True
)
```

## Initialization

To initialize the database and create sample data:

```bash
python scripts/init_audit_db.py
```

## Configuration

The system uses the following environment variables:

- `DATABASE_URL` - Database connection URL (default: `sqlite:///itsm_audit.db`)

Example `.env` file:

```
DATABASE_URL=postgresql://username:password@localhost:5432/itsm_audit
```

For local testing, you can use SQLite:

```
DATABASE_URL=sqlite:///itsm_audit.db
```

For production, you should use PostgreSQL:

```
DATABASE_URL=postgresql://username:password@hostname:port/database_name
```

The system will automatically create and use the "itsm" schema in PostgreSQL. All tables will be created within this schema, helping to organize your database and avoid conflicts with other applications using the same database.

## Example

See `examples/audit_trail_example.py` for a complete example of how to use the audit trail system in a tool.
