# AWS OpenSearch Policy Tool Documentation

## Overview

The AWS OpenSearch Policy Tool provides secure integration with AWS OpenSearch for HR policy management using vector embeddings and semantic search capabilities. This tool enables intelligent policy search, retrieval, and management through natural language queries.

## Features

### 🔍 **Advanced Search Capabilities**
- **Vector Similarity Search**: Semantic search using sentence embeddings
- **Hybrid Search**: Combines vector similarity with keyword matching
- **Category Filtering**: Filter results by policy categories
- **Relevance Scoring**: Ranked results based on similarity scores

### 🔒 **Security Features**
- **AWS IAM Integration**: Secure authentication using AWS credentials
- **AWS4Auth**: Proper request signing for OpenSearch access
- **Environment-based Configuration**: Secure credential management
- **Mock Mode**: Safe development without real AWS resources

### 📊 **Data Management**
- **Policy Indexing**: Structured storage of HR policies with metadata
- **Vector Embeddings**: Automatic generation of semantic embeddings
- **Session State Management**: Context preservation across interactions
- **Audit Logging**: Comprehensive logging for all operations

## Configuration

### Environment Variables

```bash
# AWS Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your-access-key-id
AWS_SECRET_ACCESS_KEY=your-secret-access-key

# AWS OpenSearch Configuration
OPENSEARCH_ENDPOINT=https://your-opensearch-domain.us-east-1.es.amazonaws.com
OPENSEARCH_POLICY_INDEX=hr-policies
EMBEDDING_MODEL=all-MiniLM-L6-v2
```

### Required Dependencies

```bash
# Install required packages
pip install boto3 requests-aws4auth sentence-transformers numpy opensearch-py
```

## Usage Examples

### 1. Vector Similarity Search

```python
from tools.aws_opensearch_policy_tool import search_hr_policies_vector

# Search for leave policies using natural language
result = search_hr_policies_vector(
    query="What is the annual leave policy for full-time employees?",
    top_k=5,
    category_filter="Leave"
)

print(f"Found {len(result['results'])} policies")
for policy in result['results']:
    print(f"- {policy['title']} (Score: {policy['relevance_score']})")
```

### 2. Hybrid Search

```python
from tools.aws_opensearch_policy_tool import search_hr_policies_hybrid

# Combine semantic and keyword search
result = search_hr_policies_hybrid(
    query="work from home remote policy",
    top_k=3
)

for policy in result['results']:
    print(f"Policy: {policy['title']}")
    print(f"Category: {policy['category']}")
    print(f"Summary: {policy['summary']}")
```

### 3. Get Policy by ID

```python
from tools.aws_opensearch_policy_tool import get_policy_by_id_opensearch

# Retrieve specific policy
result = get_policy_by_id_opensearch("POL-001")

if result['status'] == 'success':
    policy = result['policy']
    print(f"Title: {policy['title']}")
    print(f"Content: {policy['content']}")
```

### 4. Get Policies by Category

```python
from tools.aws_opensearch_policy_tool import get_policies_by_category_opensearch

# Get all leave policies
result = get_policies_by_category_opensearch(
    category="Leave",
    top_k=10
)

print(f"Found {len(result['policies'])} leave policies")
```

## OpenSearch Index Structure

### Policy Document Schema

```json
{
  "policy_id": "POL-001",
  "title": "Annual Leave Policy",
  "category": "Leave",
  "subcategory": "Annual Leave",
  "content": "Full policy text content...",
  "summary": "Brief policy summary",
  "effective_date": "2023-01-01",
  "last_updated": "2023-01-01",
  "embedding": [0.1, 0.2, 0.3, ...],  // 384-dimensional vector
  "tags": ["leave", "vacation", "time-off"],
  "status": "active"
}
```

### Index Mapping

```json
{
  "mappings": {
    "properties": {
      "policy_id": {"type": "keyword"},
      "title": {"type": "text", "analyzer": "standard"},
      "category": {"type": "keyword"},
      "subcategory": {"type": "keyword"},
      "content": {"type": "text", "analyzer": "standard"},
      "summary": {"type": "text", "analyzer": "standard"},
      "effective_date": {"type": "date"},
      "last_updated": {"type": "date"},
      "embedding": {
        "type": "knn_vector",
        "dimension": 384,
        "method": {
          "name": "hnsw",
          "space_type": "cosinesimil"
        }
      },
      "tags": {"type": "keyword"},
      "status": {"type": "keyword"}
    }
  }
}
```

## Security Best Practices

### 1. AWS IAM Permissions

Create an IAM policy with minimal required permissions:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "es:ESHttpGet",
        "es:ESHttpPost",
        "es:ESHttpPut"
      ],
      "Resource": "arn:aws:es:us-east-1:account-id:domain/your-domain/*"
    }
  ]
}
```

### 2. Network Security

- Use VPC endpoints for OpenSearch access
- Configure security groups to restrict access
- Enable encryption in transit and at rest

### 3. Authentication Methods

The tool supports multiple authentication methods:

1. **Explicit Credentials**: AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY
2. **IAM Roles**: For EC2 instances or Lambda functions
3. **AWS Profiles**: For local development

## Error Handling

The tool includes comprehensive error handling:

```python
# Example error response
{
  "status": "error",
  "message": "OpenSearch connection failed: Connection timeout",
  "results": []
}
```

Common error scenarios:
- **Connection Errors**: Network connectivity issues
- **Authentication Errors**: Invalid AWS credentials
- **Index Errors**: Missing or misconfigured index
- **Query Errors**: Malformed search queries

## Performance Optimization

### 1. Embedding Model Selection

- **all-MiniLM-L6-v2**: Fast, good quality (default)
- **all-mpnet-base-v2**: Higher quality, slower
- **distilbert-base-nli-stsb-mean-tokens**: Balanced performance

### 2. Search Optimization

- Use category filters to reduce search space
- Limit top_k to reasonable values (5-10)
- Cache frequently accessed policies
- Use hybrid search for better relevance

### 3. Index Optimization

- Configure appropriate shard and replica settings
- Use index templates for consistent mapping
- Implement index lifecycle management
- Monitor index performance metrics

## Monitoring and Logging

The tool provides comprehensive logging:

```python
# Example log output
2023-01-01 10:00:00 - INFO - === HR POLICY VECTOR SEARCH ===
2023-01-01 10:00:00 - INFO - Query: annual leave policy
2023-01-01 10:00:00 - INFO - Top K: 5
2023-01-01 10:00:00 - INFO - Category Filter: Leave
2023-01-01 10:00:01 - INFO - Found 3 policies for vector query: annual leave policy
2023-01-01 10:00:01 - INFO - Policy search results stored in session state
```

## Testing

Run the test suite:

```bash
# Run all tests
pytest tests/test_aws_opensearch_tool.py

# Run specific test
pytest tests/test_aws_opensearch_tool.py::TestAWSOpenSearchPolicyClient::test_client_initialization

# Run with coverage
pytest --cov=tools.aws_opensearch_policy_tool tests/test_aws_opensearch_tool.py
```

## Troubleshooting

### Common Issues

1. **Mock Mode Activation**
   - Check OPENSEARCH_ENDPOINT configuration
   - Verify AWS credentials
   - Review network connectivity

2. **Embedding Model Errors**
   - Ensure sentence-transformers is installed
   - Check model download permissions
   - Verify disk space for model storage

3. **Search Quality Issues**
   - Review embedding model selection
   - Adjust search parameters
   - Consider hybrid search approach

### Debug Mode

Enable debug logging:

```python
import logging
logging.getLogger('tools.aws_opensearch_policy_tool').setLevel(logging.DEBUG)
```

## Integration with HR Agents

The tool integrates seamlessly with HR agents:

```python
# In agent configuration
from tools.aws_opensearch_policy_tool import (
    search_hr_policies_vector,
    search_hr_policies_hybrid,
    get_policy_by_id_opensearch
)

tools = [
    search_hr_policies_vector,
    search_hr_policies_hybrid,
    get_policy_by_id_opensearch
]
```

This enables agents to provide intelligent policy assistance with natural language understanding and semantic search capabilities.
