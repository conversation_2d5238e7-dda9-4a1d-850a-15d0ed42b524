# Dynamic WagonHR API Agent Documentation

## Overview

The Dynamic WagonHR API Agent is an intelligent system that automatically reads Swagger/OpenAPI specifications and dynamically calls WagonHR APIs based on natural language user requests. Instead of manually creating individual tools for each API endpoint, this agent can understand user intent and automatically:

1. **Parse Swagger Specifications**: Automatically load and understand API endpoints
2. **Match User Intent**: Find the most relevant API endpoint based on user requests
3. **Extract Parameters**: Intelligently extract parameters from natural language
4. **Make API Calls**: Construct and execute API calls with proper authentication
5. **Format Responses**: Present results in user-friendly formats

## Key Features

### 🤖 **Intelligent Endpoint Matching**
- Analyzes user requests using natural language processing
- Scores and ranks API endpoints based on relevance
- Handles synonyms and variations in user language

### 📝 **Smart Parameter Extraction**
- Extracts dates, leave types, reasons, and other parameters from text
- Supports multiple date formats (YYYY-MM-DD, MM/DD/YYYY, etc.)
- Identifies context-specific information automatically

### 🔒 **Secure Authentication**
- Integrates with existing HR authentication manager
- Handles WagonHR token management automatically
- Includes proper headers (tenant ID, entity ID)

### 📊 **Response Formatting**
- Converts raw API responses to user-friendly formats
- Provides structured output for leave balances, history, etc.
- Includes helpful error messages and suggestions

## Architecture

```
User Request → Intent Analysis → Endpoint Selection → Parameter Extraction → API Call → Response Formatting
```

### Core Components

#### **1. DynamicWagonHRAgent Class**
- Main orchestrator for dynamic API interactions
- Loads and parses Swagger specifications
- Manages endpoint mapping and scoring

#### **2. Natural Language Processing**
- Keyword-based intent recognition
- Parameter extraction using regex patterns
- Context-aware parameter assignment

#### **3. API Integration**
- Dynamic URL construction
- Automatic header management
- Error handling and retry logic

## Usage Examples

### Basic Usage

```python
from tools.dynamic_wagonhr_api_agent import smart_wagonhr_assistant

# Leave balance inquiry
result = smart_wagonhr_assistant("How many leave days do I have?")
print(result["formatted_response"])
# Output: Your current leave balance:
#         • Casual: 5 days
#         • Sick: 3 days
#         • Annual: 10 days

# Leave request submission
result = smart_wagonhr_assistant(
    "I want to apply for casual leave from 2024-01-15 to 2024-01-17 because I'm sick"
)

# Attendance recording
result = smart_wagonhr_assistant("Record my attendance as present")
```

### Advanced Usage with Context

```python
# With session context for state management
mock_context = Mock()
mock_context.state = {}

result = smart_wagonhr_assistant(
    "Check my leave balance", 
    tool_context=mock_context
)

# Previous request details are stored in context
last_request = mock_context.state["last_wagonhr_request"]
```

## Supported Request Types

### 🏖️ **Leave Management**

| User Request | Detected Intent | API Endpoint |
|-------------|----------------|--------------|
| "How many leave days do I have?" | Leave Balance | GET /api/hrms/leave/balance |
| "Apply for casual leave from 2024-01-15 to 2024-01-17" | Leave Request | POST /api/hrms/leave/request |
| "Show my leave history" | Leave History | GET /api/hrms/leave/history |
| "Cancel my leave request" | Leave Cancellation | DELETE /api/hrms/leave/request/{id} |

### 👥 **Attendance Management**

| User Request | Detected Intent | API Endpoint |
|-------------|----------------|--------------|
| "Record my attendance as present" | Attendance Recording | POST /api/hrms/attendance/record |
| "Show my attendance report" | Attendance Report | GET /api/hrms/attendance/report |
| "Check in for today" | Check In | POST /api/hrms/attendance/checkin |
| "Mark me absent yesterday" | Attendance Update | PUT /api/hrms/attendance/update |

### 💼 **Employee Information**

| User Request | Detected Intent | API Endpoint |
|-------------|----------------|--------------|
| "Show my profile" | Employee Profile | GET /api/hrms/employee/profile |
| "Get my manager details" | Manager Info | GET /api/hrms/employee/manager |
| "Update my contact information" | Profile Update | PUT /api/hrms/employee/profile |

## Parameter Extraction

### Date Recognition
```python
# Supported date formats
"2024-01-15"           # YYYY-MM-DD
"01/15/2024"          # MM/DD/YYYY  
"15-01-2024"          # DD-MM-YYYY
"from Monday to Friday" # Natural language (future enhancement)
```

### Leave Type Detection
```python
# Recognized leave types
"casual", "sick", "annual", "vacation", "personal", 
"maternity", "paternity", "emergency", "bereavement"
```

### Reason Extraction
```python
# Pattern matching for reasons
"because I'm sick"     → reason: "I'm sick"
"for medical treatment" → reason: "medical treatment"
"due to family emergency" → reason: "family emergency"
```

## Configuration

### Environment Variables
```bash
# WagonHR API Configuration
WAGONHR_BASE_URL=https://dev-api-wagonhr.mouritech.net
WAGONHR_SWAGGER_URL=https://dev-api-wagonhr.mouritech.net/api/hrms/v3/api-docs

# Authentication
WAGONHR_TENANT_ID=mouritech
WAGONHR_ENTITY_ID=US-MT-002
```

### Swagger Specification Loading
```python
# The agent automatically tries multiple endpoints:
possible_endpoints = [
    f"{base_url}/api/hrms/v3/api-docs",
    f"{base_url}/api/hrms/swagger-ui/api-docs", 
    f"{base_url}/v3/api-docs",
    f"{base_url}/swagger/v1/swagger.json"
]
```

## Error Handling

### Common Error Scenarios

#### **1. No Matching Endpoint**
```json
{
  "status": "error",
  "message": "Could not find a relevant API endpoint for your request",
  "suggestion": "Please try rephrasing your request or be more specific",
  "available_operations": ["getLeaveBalance", "submitLeaveRequest", ...]
}
```

#### **2. Authentication Failure**
```json
{
  "status": "error", 
  "message": "Authentication token not available"
}
```

#### **3. API Call Failure**
```json
{
  "status": "error",
  "message": "API call failed with status 400",
  "details": "Invalid request parameters"
}
```

## Response Formatting

### Leave Balance Response
```python
# Raw API response
{
  "leave_balance": {
    "casual": 5,
    "sick": 3, 
    "annual": 10
  }
}

# Formatted response
"Your current leave balance:
• Casual: 5 days
• Sick: 3 days  
• Annual: 10 days"
```

### Leave History Response
```python
# Raw API response
[
  {
    "startDate": "2024-01-15",
    "endDate": "2024-01-17", 
    "leaveType": "casual",
    "status": "approved"
  }
]

# Formatted response
"Your leave history:
• Casual from 2024-01-15 to 2024-01-17 - approved"
```

## Integration with HR Agents

### Agent Tool Registration
```python
# In agent configuration
from tools.dynamic_wagonhr_api_agent import smart_wagonhr_assistant

tools = [
    smart_wagonhr_assistant,
    # Other tools...
]
```

### Session State Management
```python
# The agent automatically stores request history
tool_context.state["last_wagonhr_request"] = {
    "user_request": "Check my leave balance",
    "endpoint": "Get leave balance", 
    "parameters": {},
    "result": {...},
    "timestamp": "2024-01-15T10:30:00"
}
```

## Benefits Over Manual Tool Creation

### ✅ **Advantages**

1. **Automatic API Discovery**: No need to manually create tools for each endpoint
2. **Self-Updating**: Automatically adapts to API changes via Swagger
3. **Natural Language Processing**: Understands user intent without rigid syntax
4. **Reduced Maintenance**: Single agent handles all WagonHR operations
5. **Consistent Authentication**: Centralized token management
6. **Smart Error Handling**: Provides helpful suggestions and guidance

### 📈 **Scalability**

- **New Endpoints**: Automatically available without code changes
- **API Updates**: Swagger changes are reflected immediately  
- **Multiple APIs**: Can be extended to other API specifications
- **Language Support**: Easy to add new language patterns

## Testing

### Unit Tests
```bash
# Run all tests
pytest tests/test_dynamic_wagonhr_agent.py

# Run specific test categories
pytest tests/test_dynamic_wagonhr_agent.py::TestDynamicWagonHRAgent
pytest tests/test_dynamic_wagonhr_agent.py::TestSmartWagonHRAssistant
```

### Integration Tests
```bash
# Test with real API (requires authentication)
pytest tests/test_dynamic_wagonhr_agent.py::TestIntegration
```

## Future Enhancements

### 🚀 **Planned Features**

1. **Multi-Language Support**: Support for multiple natural languages
2. **Advanced NLP**: Integration with language models for better intent recognition
3. **Workflow Automation**: Chain multiple API calls for complex operations
4. **Caching**: Response caching for improved performance
5. **Analytics**: Usage analytics and optimization suggestions

### 🔧 **Technical Improvements**

1. **Schema Validation**: Automatic request/response validation
2. **Rate Limiting**: Built-in rate limiting and retry logic
3. **Monitoring**: Performance monitoring and alerting
4. **Documentation Generation**: Auto-generate user documentation from Swagger

## Conclusion

The Dynamic WagonHR API Agent represents a significant advancement in HR system integration, providing:

- **Simplified Development**: No manual tool creation required
- **Enhanced User Experience**: Natural language interaction
- **Automatic Adaptation**: Self-updating based on API changes
- **Robust Error Handling**: Comprehensive error management
- **Scalable Architecture**: Easy to extend and maintain

This approach transforms how users interact with HR systems, making complex API operations accessible through simple, natural language requests.
