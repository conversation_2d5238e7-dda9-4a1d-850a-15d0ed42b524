#!/usr/bin/env python3
"""
Test script for the Dynamic WagonHR API Agent.

This script tests the real-time dynamic agent that fetches Swagger specifications
and makes actual API calls to WagonHR.
"""

import sys
import os
import logging

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_dynamic_agent():
    """Test the dynamic WagonHR agent."""
    try:
        logger.info("=== TESTING DYNAMIC WAGONHR AGENT ===")
        
        # Import the dynamic agent
        from tools.dynamic_wagonhr_api_agent import (
            dynamic_wagonhr_agent,
            smart_wagonhr_assistant,
            test_wagonhr_connectivity,
            refresh_api_specification
        )
        
        logger.info("Dynamic agent imported successfully")
        
        # Test 1: Check connectivity
        logger.info("\n--- Test 1: Testing WagonHR Connectivity ---")
        connectivity_result = test_wagonhr_connectivity()
        logger.info(f"Connectivity Result: {connectivity_result}")
        
        # Test 2: Refresh API specification
        logger.info("\n--- Test 2: Refreshing API Specification ---")
        refresh_result = refresh_api_specification()
        logger.info(f"Refresh Result: {refresh_result}")
        
        # Test 3: Check available endpoints
        logger.info("\n--- Test 3: Available Endpoints ---")
        logger.info(f"Total endpoints discovered: {len(dynamic_wagonhr_agent.endpoints)}")
        for operation_id, endpoint in dynamic_wagonhr_agent.endpoints.items():
            logger.info(f"  - {operation_id}: {endpoint['summary']} ({endpoint['method']} {endpoint['path']})")
        
        # Test 4: Test help request
        logger.info("\n--- Test 4: Testing Help Request ---")
        help_result = smart_wagonhr_assistant("What can you help me with?")
        logger.info(f"Help Result: {help_result}")
        
        # Test 5: Test leave balance request
        logger.info("\n--- Test 5: Testing Leave Balance Request ---")
        balance_result = smart_wagonhr_assistant("How many leave days do I have?")
        logger.info(f"Balance Result: {balance_result}")
        
        # Test 6: Test leave request submission
        logger.info("\n--- Test 6: Testing Leave Request Submission ---")
        leave_request_result = smart_wagonhr_assistant(
            "I want to apply for casual leave from 2024-02-15 to 2024-02-17 because I'm sick"
        )
        logger.info(f"Leave Request Result: {leave_request_result}")
        
        # Test 7: Test attendance recording
        logger.info("\n--- Test 7: Testing Attendance Recording ---")
        attendance_result = smart_wagonhr_assistant("Record my attendance as present")
        logger.info(f"Attendance Result: {attendance_result}")
        
        logger.info("\n=== DYNAMIC AGENT TESTING COMPLETED ===")
        
    except Exception as e:
        logger.error(f"Error testing dynamic agent: {e}")
        import traceback
        traceback.print_exc()

def test_swagger_fetching():
    """Test direct Swagger specification fetching."""
    try:
        logger.info("=== TESTING SWAGGER FETCHING ===")
        
        import requests
        
        # Test different Swagger endpoints
        base_url = "https://dev-api-wagonhr.mouritech.net"
        endpoints_to_test = [
            f"{base_url}/api/hrms/v3/api-docs",
            f"{base_url}/api/hrms/v3/api-docs/Leave%20%26%20Attendance%20Mgmt",
            f"{base_url}/v3/api-docs",
            f"{base_url}/swagger-ui/api-docs",
            f"{base_url}/swagger/v1/swagger.json"
        ]
        
        headers = {
            "Accept": "application/json",
            "Content-Type": "application/json",
            "User-Agent": "HR-Dynamic-Agent-Test/1.0"
        }
        
        for endpoint in endpoints_to_test:
            try:
                logger.info(f"Testing endpoint: {endpoint}")
                response = requests.get(endpoint, headers=headers, timeout=10)
                logger.info(f"  Status: {response.status_code}")
                logger.info(f"  Headers: {dict(response.headers)}")
                
                if response.status_code == 200:
                    try:
                        json_data = response.json()
                        logger.info(f"  JSON Response: Successfully parsed")
                        logger.info(f"  Keys: {list(json_data.keys()) if isinstance(json_data, dict) else 'Not a dict'}")
                        if isinstance(json_data, dict) and "paths" in json_data:
                            logger.info(f"  Paths found: {len(json_data['paths'])}")
                    except:
                        logger.info(f"  Response (first 200 chars): {response.text[:200]}")
                else:
                    logger.info(f"  Response (first 200 chars): {response.text[:200]}")
                    
            except Exception as e:
                logger.error(f"  Error: {e}")
        
        logger.info("=== SWAGGER FETCHING TEST COMPLETED ===")
        
    except Exception as e:
        logger.error(f"Error testing Swagger fetching: {e}")

def test_endpoint_discovery():
    """Test endpoint discovery by probing."""
    try:
        logger.info("=== TESTING ENDPOINT DISCOVERY ===")
        
        import requests
        
        base_url = "https://dev-api-wagonhr.mouritech.net"
        
        # Test common endpoints
        test_endpoints = [
            "/api/hrms/leave/balance",
            "/api/hrms/leave/request", 
            "/api/hrms/leave/history",
            "/api/hrms/attendance/record",
            "/api/hrms/attendance/report",
            "/api/hrms/employee/profile",
            "/api/hrms/health",
            "/health",
            "/api/health"
        ]
        
        headers = {
            "Accept": "application/json",
            "User-Agent": "HR-Dynamic-Agent-Test/1.0"
        }
        
        discovered_endpoints = []
        
        for endpoint in test_endpoints:
            try:
                url = f"{base_url}{endpoint}"
                logger.info(f"Probing: {url}")
                
                # Try OPTIONS request first
                options_response = requests.options(url, headers=headers, timeout=5)
                logger.info(f"  OPTIONS: {options_response.status_code}")
                
                # Try GET request
                get_response = requests.get(url, headers=headers, timeout=5)
                logger.info(f"  GET: {get_response.status_code}")
                
                # Consider endpoint discovered if we get any response other than 404
                if options_response.status_code != 404 or get_response.status_code != 404:
                    discovered_endpoints.append(endpoint)
                    logger.info(f"  ✓ DISCOVERED: {endpoint}")
                
            except Exception as e:
                logger.info(f"  Error: {e}")
        
        logger.info(f"\nDiscovered {len(discovered_endpoints)} endpoints:")
        for endpoint in discovered_endpoints:
            logger.info(f"  - {endpoint}")
        
        logger.info("=== ENDPOINT DISCOVERY TEST COMPLETED ===")
        
    except Exception as e:
        logger.error(f"Error testing endpoint discovery: {e}")

if __name__ == "__main__":
    logger.info("Starting Dynamic WagonHR Agent Tests...")
    
    # Run all tests
    test_swagger_fetching()
    print("\n" + "="*50 + "\n")
    
    test_endpoint_discovery()
    print("\n" + "="*50 + "\n")
    
    test_dynamic_agent()
    
    logger.info("All tests completed!")
