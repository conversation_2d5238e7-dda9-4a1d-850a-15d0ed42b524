[build-system]
requires = ["setuptools>=45", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "hr-solution"
version = "2.1.82"
description = "HR AI Assistant with Google ADK integration"
authors = [
    {name = "Mouritech", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.8"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
]

dependencies = [
    "fastapi>=0.68.0",
    "uvicorn[standard]>=0.15.0",
    "pydantic>=1.8.0",
    "sqlalchemy>=1.4.0",
    "google-adk[vertexai]>=0.1.0",
    "requests>=2.25.0",
    "python-dotenv>=0.19.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=6.0",
    "pytest-asyncio>=0.15.0",
    "black>=21.0.0",
    "flake8>=3.9.0",
]

[project.urls]
Homepage = "https://github.com/mouritech/hr-solution"
Documentation = "https://hr-solution.mouritech.com/docs"
Repository = "https://github.com/mouritech/hr-solution.git"
"Bug Tracker" = "https://github.com/mouritech/hr-solution/issues"

[tool.setuptools.packages.find]
where = ["."]
include = ["*"]
exclude = ["tests*", "docs*"]
