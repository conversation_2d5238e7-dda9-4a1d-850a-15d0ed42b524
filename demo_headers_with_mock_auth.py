#!/usr/bin/env python3
"""
Demo script showing complete header flow with mock authentication.

This demonstrates exactly how headers including Authorization Bearer token
flow through the system when authentication is successful.
"""

import os
import sys
import time

# Add the project root to the path
sys.path.append('.')

from utils.hr_api_client import HRAPIClient
from utils.hr_auth import hr_auth

def demo_with_mock_auth():
    """Demo the complete header flow with mock authentication."""
    
    print("🎭 Demo: Complete Header Flow with Mock Authentication")
    print("=" * 65)
    
    # 1. Mock successful authentication
    print("\n1️⃣ Setting up mock authentication:")
    
    # Mock WagonHR token
    mock_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.mock_payload.mock_signature"
    hr_auth.wagonhr_access_token = mock_token
    hr_auth.wagonhr_token_expiry = time.time() + 3600  # Valid for 1 hour
    
    print(f"   ✅ Mock WagonHR token set: {mock_token[:20]}...{mock_token[-10:]}")
    print(f"   ✅ Token expiry: {hr_auth.wagonhr_token_expiry}")
    
    # 2. Create client with environment headers
    print("\n2️⃣ Setting up client with environment headers:")
    os.environ["HR_TENANT_ID"] = "prod-tenant-123"
    os.environ["HR_ENTITY_ID"] = "prod-entity-456"
    
    client = HRAPIClient(auth_type="wagonhr")
    print(f"   Default headers: {client.default_headers}")
    
    # 3. Set additional tenant context
    print("\n3️⃣ Setting additional tenant context:")
    client.set_tenant_context(
        tenant_id="override-tenant-999",
        entity_id="override-entity-888",
        organization_id="override-org-777"
    )
    print(f"   Updated headers: {client.default_headers}")
    
    # 4. Prepare custom request headers
    print("\n4️⃣ Preparing custom request headers:")
    custom_headers = {
        "requestId": "demo-req-54321",
        "correlationId": "demo-corr-98765",
        "userAgent": "HR-Demo-Client/2.0",
        "clientVersion": "2.1.0"
    }
    print(f"   Custom headers: {custom_headers}")
    
    # 5. Show authentication headers
    print("\n5️⃣ Getting authentication headers:")
    auth_headers = hr_auth.get_wagonhr_auth_headers()
    print("   Authentication headers:")
    for key, value in auth_headers.items():
        if key.lower() == 'authorization':
            masked_value = f"Bearer ***{value[-15:] if len(value) > 15 else '***'}"
            print(f"   ✅ {key}: {masked_value}")
        else:
            print(f"   ✅ {key}: {value}")
    
    # 6. Show complete merged headers
    print("\n6️⃣ Complete merged headers (all combined):")
    complete_headers = client.get_all_headers(custom_headers)
    print("   Final headers for HTTP request:")
    for key, value in complete_headers.items():
        if key.lower() == 'authorization':
            masked_value = f"Bearer ***{value[-15:] if len(value) > 15 else '***'}"
            print(f"   🔐 {key}: {masked_value}")
        else:
            print(f"   📋 {key}: {value}")
    
    # 7. Show the actual HTTP request that would be made
    print("\n7️⃣ Final HTTP request structure:")
    print("   📡 HTTP Method: GET")
    print("   🌐 URL: https://dev-api-wagonhr.mouritech.net/api/hrms/leave-attendance/employee-leaves")
    print("   📋 Headers:")
    
    for key, value in complete_headers.items():
        if key.lower() == 'authorization':
            print(f"      🔐 {key}: Bearer <actual-wagonhr-token>")
        else:
            print(f"      📝 {key}: {value}")
    
    print("   🔍 Query Parameters: ?email=<EMAIL>")
    
    # 8. Show header precedence
    print("\n8️⃣ Header precedence (how conflicts are resolved):")
    print("   1. 🏠 Environment variables (HR_TENANT_ID, etc.)")
    print("   2. 🎯 set_tenant_context() calls (overrides environment)")
    print("   3. 📝 Per-request custom headers (overrides context)")
    print("   4. 🔐 Authentication headers (added by auth manager)")
    print("   5. 🚀 Final HTTP request (all combined)")
    
    return complete_headers

def demo_microsoft_auth():
    """Demo with Microsoft authentication."""
    
    print("\n🔷 Demo: Microsoft Authentication Headers")
    print("=" * 50)
    
    # Mock Microsoft token
    mock_ms_token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.mock_ms_payload.mock_ms_signature"
    hr_auth.set_microsoft_token(mock_ms_token, time.time() + 3600)
    
    print(f"   ✅ Mock Microsoft token set: {mock_ms_token[:25]}...{mock_ms_token[-10:]}")
    
    # Create client with Microsoft auth
    ms_client = HRAPIClient(auth_type="microsoft")
    ms_client.set_tenant_context("ms-tenant-123", "ms-entity-456")
    
    # Get Microsoft auth headers
    ms_auth_headers = hr_auth.get_ms_auth_headers()
    print("   Microsoft authentication headers:")
    for key, value in ms_auth_headers.items():
        if key.lower() == 'authorization':
            masked_value = f"Bearer ***{value[-15:] if len(value) > 15 else '***'}"
            print(f"   🔐 {key}: {masked_value}")
        else:
            print(f"   📋 {key}: {value}")

def show_real_world_example():
    """Show a real-world example of how this would be used."""
    
    print("\n🌍 Real-World Usage Example")
    print("=" * 40)
    
    print("In a real application, you would:")
    print("1. 🔧 Set environment variables in .env:")
    print("   HR_TENANT_ID=your-tenant-id")
    print("   HR_ENTITY_ID=your-entity-id")
    print("   WAGONHR_USERNAME=your-username")
    print("   WAGONHR_PASSWORD=your-password")
    
    print("\n2. 🏗️ Initialize client (automatic auth):")
    print("   from utils.hr_api_client import leave_balance_client")
    
    print("\n3. 📞 Make API calls with custom headers:")
    print("   result = leave_balance_client.get_leave_balance(")
    print("       email='<EMAIL>',")
    print("       headers={'requestId': 'req-123', 'tenantId': 'special-tenant'}")
    print("   )")
    
    print("\n4. 🚀 System automatically handles:")
    print("   ✅ Authentication (Bearer token)")
    print("   ✅ Header merging (default + custom)")
    print("   ✅ HTTP request construction")
    print("   ✅ Response handling")

if __name__ == "__main__":
    complete_headers = demo_with_mock_auth()
    demo_microsoft_auth()
    show_real_world_example()
    
    print("\n" + "=" * 65)
    print("🎉 Demo completed!")
    print("💡 This shows exactly how Authorization Bearer tokens and other headers")
    print("   flow through the system from environment → context → request → HTTP")
    print(f"🔍 Total headers in final request: {len(complete_headers)}")
    print("🔐 Authorization header is automatically included in all API calls!")
