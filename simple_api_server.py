"""
Simple API Server for HR AI Assistant Solution.

This module provides a FastAPI server for the HR AI solution.
"""

import logging
import os
from typing import Dict, Any, List, Optional
from datetime import datetime

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="Virtual HR AI Assistant API",
    description="API for the Virtual HR AI Assistant",
    version="0.1.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

# Models for leave management
class LeaveRequest(BaseModel):
    """Model for a leave request."""
    employee_email: str
    start_date: str
    end_date: str
    leave_type: str
    reason: str
    half_day: bool = False

class LeaveResponse(BaseModel):
    """Response model for leave operations."""
    status: str
    message: str
    leave_id: Optional[str] = None
    leave_request: Optional[Dict[str, Any]] = None
    hr_case_id: Optional[str] = None

class LeaveApproval(BaseModel):
    """Model for leave approval."""
    leave_id: str
    approver_email: str
    notes: Optional[str] = None

class LeaveRejection(BaseModel):
    """Model for leave rejection."""
    leave_id: str
    approver_email: str
    reason: str

class LeaveBalanceRequest(BaseModel):
    """Model for leave balance request."""
    employee_email: str

class LeaveHistoryRequest(BaseModel):
    """Model for leave history request."""
    employee_email: str
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    status: Optional[str] = None

class PolicySearchRequest(BaseModel):
    """Model for policy search request."""
    query: str
    top_k: int = 5

class PolicyByIdRequest(BaseModel):
    """Model for policy by ID request."""
    policy_id: str

class PolicyByCategoryRequest(BaseModel):
    """Model for policy by category request."""
    category: str

class LeaveSuggestionRequest(BaseModel):
    """Model for leave suggestion request."""
    employee_email: str
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    duration_days: int = 5

class WorkloadCheckRequest(BaseModel):
    """Model for workload check request."""
    employee_email: str
    start_date: str
    end_date: str

class AttendanceRecordRequest(BaseModel):
    """Model for attendance record request."""
    employee_email: str
    status: str
    timestamp: Optional[str] = None
    location: Optional[Dict[str, float]] = None
    notes: Optional[str] = None

class AttendanceReportRequest(BaseModel):
    """Model for attendance report request."""
    employee_email: str
    start_date: str
    end_date: str

class AttendanceAnomalyRequest(BaseModel):
    """Model for attendance anomaly detection request."""
    employee_email: Optional[str] = None
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    anomaly_types: Optional[List[str]] = None

# Routes
@app.get("/")
async def root():
    """Root endpoint."""
    return {"message": "Welcome to the HR AI Service Desk API"}

# Leave Management Routes
@app.post("/leave/request", response_model=LeaveResponse)
async def request_leave(request: LeaveRequest):
    """Create a new leave request."""
    try:
        from tools.leave_tools import request_leave as leave_request_tool

        result = leave_request_tool(
            employee_email=request.employee_email,
            start_date=request.start_date,
            end_date=request.end_date,
            leave_type=request.leave_type,
            reason=request.reason,
            half_day=request.half_day
        )

        return LeaveResponse(**result)
    except Exception as e:
        logger.error(f"Error creating leave request: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/leave/approve", response_model=LeaveResponse)
async def approve_leave(request: LeaveApproval):
    """Approve a leave request."""
    try:
        from tools.leave_tools import approve_leave as leave_approve_tool

        result = leave_approve_tool(
            leave_id=request.leave_id,
            approver_email=request.approver_email,
            notes=request.notes
        )

        return LeaveResponse(**result)
    except Exception as e:
        logger.error(f"Error approving leave request: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/leave/reject", response_model=LeaveResponse)
async def reject_leave(request: LeaveRejection):
    """Reject a leave request."""
    try:
        from tools.leave_tools import reject_leave as leave_reject_tool

        result = leave_reject_tool(
            leave_id=request.leave_id,
            approver_email=request.approver_email,
            reason=request.reason
        )

        return LeaveResponse(**result)
    except Exception as e:
        logger.error(f"Error rejecting leave request: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/leave/balance", response_model=Dict[str, Any])
async def get_leave_balance(request: LeaveBalanceRequest):
    """Get leave balance for an employee."""
    try:
        from tools.leave_tools import get_leave_balance as leave_balance_tool

        result = leave_balance_tool(
            employee_email=request.employee_email
        )

        return result
    except Exception as e:
        logger.error(f"Error getting leave balance: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/leave/history", response_model=Dict[str, Any])
async def get_leave_history(request: LeaveHistoryRequest):
    """Get leave history for an employee."""
    try:
        from tools.leave_tools import get_leave_history as leave_history_tool

        result = leave_history_tool(
            employee_email=request.employee_email,
            start_date=request.start_date,
            end_date=request.end_date,
            status=request.status
        )

        return result
    except Exception as e:
        logger.error(f"Error getting leave history: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Policy Search Routes
@app.post("/policy/search", response_model=Dict[str, Any])
async def search_policies(request: PolicySearchRequest):
    """Search for HR policies."""
    try:
        from tools.policy_search_tools import search_hr_policies

        result = search_hr_policies(
            query=request.query,
            top_k=request.top_k
        )

        return result
    except Exception as e:
        logger.error(f"Error searching policies: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/policy/by-id", response_model=Dict[str, Any])
async def get_policy_by_id(request: PolicyByIdRequest):
    """Get an HR policy by ID."""
    try:
        from tools.policy_search_tools import get_hr_policy_by_id

        result = get_hr_policy_by_id(
            policy_id=request.policy_id
        )

        return result
    except Exception as e:
        logger.error(f"Error getting policy by ID: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/policy/by-category", response_model=Dict[str, Any])
async def get_policies_by_category(request: PolicyByCategoryRequest):
    """Get HR policies by category."""
    try:
        from tools.policy_search_tools import get_hr_policies_by_category

        result = get_hr_policies_by_category(
            category=request.category
        )

        return result
    except Exception as e:
        logger.error(f"Error getting policies by category: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Workload-based Leave Suggestion Routes
@app.post("/leave/suggest", response_model=Dict[str, Any])
async def suggest_leave_dates(request: LeaveSuggestionRequest):
    """Suggest optimal leave dates based on workload."""
    try:
        # Mock implementation
        from datetime import datetime, timedelta

        today = datetime.now().date()
        suggested_start = today + timedelta(days=14)  # Two weeks from now
        suggested_end = suggested_start + timedelta(days=request.duration_days - 1)

        result = {
            "status": "success",
            "message": "Leave dates suggested successfully",
            "employee_email": request.employee_email,
            "suggested_dates": [
                {
                    "start_date": suggested_start.isoformat(),
                    "end_date": suggested_end.isoformat(),
                    "workload_score": 0.2,  # Low workload (good time for leave)
                    "reason": "Low project activity during this period"
                },
                {
                    "start_date": (suggested_start + timedelta(days=14)).isoformat(),
                    "end_date": (suggested_end + timedelta(days=14)).isoformat(),
                    "workload_score": 0.3,
                    "reason": "No critical deadlines during this period"
                }
            ]
        }

        return result
    except Exception as e:
        logger.error(f"Error suggesting leave dates: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "simple_api_server:app",
        host="0.0.0.0",
        port=int(os.environ.get("PORT", 8002)),
        reload=False
    )
